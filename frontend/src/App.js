import React, { useState, useEffect, lazy, Suspense } from 'react';
import CodeInput from './components/CodeInput';
import LoadingIndicator from './components/LoadingIndicator';
import ModelManagement from './components/ModelManagement';
import { analyzeCode, getAvailableModels } from './services/api';

const WS_URL = 'ws://localhost:8000/ws';

// Lazy-loaded components for code splitting
const Dashboard = lazy(() => import('./components/Dashboard'));
const VulnerabilityReport = lazy(() => import('./components/VulnerabilityReport'));
const ThoughtProcess = lazy(() => import('./components/ThoughtProcess'));
const ReportsHistory = lazy(() => import('./components/ReportsHistory'));
const KnowledgeSourcesManagement = lazy(() => import('./components/KnowledgeSourcesManagement'));
const BulkOperationsManagement = lazy(() => import('./components/BulkOperationsManagement'));
const AnalyticsDashboard = lazy(() => import('./components/AnalyticsDashboard'));

function App() {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [error, setError] = useState(null);
  const [selectedModel, setSelectedModel] = useState(null);
  const [currentView, setCurrentView] = useState('analyze'); // 'analyze', 'history', 'knowledge', 'bulk', or 'analytics'
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [ws, setWs] = useState(null);

  useEffect(() => {
    const fetchAndSetDefaultModel = async () => {
      try {
        const models = await getAvailableModels();
        if (models.length > 0) {
          setSelectedModel(models[0].name);
        }
      } catch (err) {
        console.error("Failed to fetch models for default selection:", err);
      }
    };
    fetchAndSetDefaultModel();
  }, []);

  useEffect(() => {
    const websocket = new WebSocket(WS_URL);

    websocket.onopen = () => {
      console.log('WebSocket Connected');
      setMessages((prevMessages) => [...prevMessages, 'Connected to WebSocket']);
    };

    websocket.onmessage = (event) => {
      console.log('Message from server:', event.data);
      setMessages((prevMessages) => [...prevMessages, `Received: ${event.data}`]);
    };

    websocket.onclose = () => {
      console.log('WebSocket Disconnected');
      setMessages((prevMessages) => [...prevMessages, 'Disconnected from WebSocket']);
    };

    websocket.onerror = (error) => {
      console.error('WebSocket Error:', error);
      setMessages((prevMessages) => [...prevMessages, `WebSocket Error: ${error.message}`]);
    };

    setWs(websocket);

    return () => {
      if (websocket.readyState === WebSocket.OPEN) {
        websocket.close();
      }
    };
  }, []);

  const sendMessage = () => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(inputValue);
      setMessages((prevMessages) => [...prevMessages, `Sent: ${inputValue}`]);
      setInputValue('');
    } else {
      console.warn('WebSocket is not open. Cannot send message.');
      setMessages((prevMessages) => [...prevMessages, 'Error: WebSocket not open.']);
    }
  };

  const handleCodeAnalysis = async (code, filePath) => {
    setIsAnalyzing(true);
    setError(null);
    setAnalysisResult(null);

    if (!selectedModel) {
      setError('Please select an LLM model before analyzing.');
      setIsAnalyzing(false);
      return;
    }

    try {
      const result = await analyzeCode(code, filePath, selectedModel);
      setAnalysisResult(result);
    } catch (err) {
      setError(err.message || 'An error occurred during analysis');
      console.error('Analysis error:', err);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleNewAnalysis = () => {
    setAnalysisResult(null);
    setError(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">
                🛡️ Sentinel Trace
              </h1>
              <span className="ml-3 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                AI-Powered Security Analysis
              </span>
            </div>

            {/* Navigation */}
            <div className="flex items-center space-x-4">
              <nav className="flex space-x-4">
                <button
                  onClick={() => setCurrentView('analyze')}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    currentView === 'analyze'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  🔍 Analyze
                </button>
                <button
                  onClick={() => setCurrentView('history')}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    currentView === 'history'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  📊 History
                </button>
                <button
                  onClick={() => setCurrentView('knowledge')}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    currentView === 'knowledge'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  🧠 Knowledge Sources
                </button>
                <button
                  onClick={() => setCurrentView('bulk')}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    currentView === 'bulk'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  ⚡ Bulk Operations
                </button>
                <button
                  onClick={() => setCurrentView('analytics')}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    currentView === 'analytics'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  📈 Analytics
                </button>
              </nav>

              {analysisResult && currentView === 'analyze' && (
                <button
                  onClick={handleNewAnalysis}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  New Analysis
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Model Management Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <ModelManagement onModelSelect={setSelectedModel} selectedModel={selectedModel} />
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentView === 'analyze' && (
          <>
            {!analysisResult && !isAnalyzing && (
              <div className="text-center mb-8">
                <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                  Deep Reasoning Protocol for Code Security Analysis
                </h2>
                <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                  Upload your code and let our AI security analyst perform a comprehensive
                  7-phase vulnerability assessment with transparent reasoning and actionable insights.
                </p>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <span className="text-red-400">⚠️</span>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      Analysis Error
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      {error}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Loading State */}
            {isAnalyzing && (
              <LoadingIndicator />
            )}

            {/* Code Input Form */}
            {!analysisResult && !isAnalyzing && (
              <CodeInput onAnalyze={handleCodeAnalysis} selectedModel={selectedModel} />
            )}

            {/* Analysis Results Dashboard */}
            {analysisResult && !isAnalyzing && (
              <Suspense fallback={<LoadingIndicator />}>
                <Dashboard result={analysisResult} />
              </Suspense>
            )}
          </>
        )}

        {/* WebSocket Integration Section */}
        <div className="mt-8 p-6 bg-white shadow rounded-lg">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            Real-time Updates (WebSocket Echo)
          </h2>
          <div className="flex mb-4">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Type a message..."
              className="flex-grow p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={sendMessage}
              className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 transition-colors"
            >
              Send Message
            </button>
          </div>
          <div className="bg-gray-100 p-4 rounded-md h-48 overflow-y-auto">
            <h3 className="text-lg font-medium text-gray-800 mb-2">Messages:</h3>
            {messages.length === 0 ? (
              <p className="text-gray-500">No messages yet.</p>
            ) : (
              <ul className="space-y-1">
                {messages.map((msg, index) => (
                  <li key={index} className="text-sm text-gray-700 break-words">
                    {msg}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>

        {currentView === 'history' && (
          <Suspense fallback={<LoadingIndicator />}>
            <ReportsHistory
              onReportSelect={(report) => {
                setAnalysisResult(report);
                setCurrentView('analyze');
              }}
            />
          </Suspense>
        )}

        {currentView === 'knowledge' && (
          <Suspense fallback={<LoadingIndicator />}>
            <KnowledgeSourcesManagement />
          </Suspense>
        )}

        {currentView === 'bulk' && (
          <Suspense fallback={<LoadingIndicator />}>
            <BulkOperationsManagement />
          </Suspense>
        )}

        {currentView === 'analytics' && (
          <Suspense fallback={<LoadingIndicator />}>
            <AnalyticsDashboard />
          </Suspense>
        )}

        {/* Image Optimization Note:
            Implement image optimization guidelines (e.g., using WebP format,
            responsive images, compression) for all static assets to improve
            frontend loading performance. This is a placeholder for documentation.
        */}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-500">
            <p>Sentinel Trace - Metacognitive AI Security Analysis</p>
            <p className="text-sm mt-2">
              Powered by Deep Reasoning Protocol & Local LLM Integration
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;
