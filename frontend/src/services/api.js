// Load API_BASE_URL from environment variable, with a fallback for development
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000';
// Load API_TOKEN from environment variable. In a real application, this would be retrieved securely (e.g., from a backend endpoint after authentication).
// For now, it's marked for replacement with an environment variable.
const API_TOKEN = process.env.REACT_APP_API_TOKEN || 'your-super-secret-token-frontend-default';

export const analyzeCode = async (code, filePath, llmType) => {
  try {
    const response = await fetch(`${API_BASE_URL}/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_TOKEN}`, // Use the API_TOKEN from environment variable
      },
      body: JSON.stringify({ code, file_path: filePath, llm_type: llmType }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to analyze code');
    }

    return await response.json();
  } catch (error) {
    console.error('Error analyzing code:', error);
    throw error;
  }
};

export const listModels = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/models/list`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to fetch available models');
    }
    const data = await response.json();
    return data.models;
  } catch (error) {
    console.error('Error fetching models:', error);
    throw error;
  }
};

export const pullModel = async (modelName) => {
  try {
    const response = await fetch(`${API_BASE_URL}/models/pull`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ model_name: modelName }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to pull model');
    }

    return await response.json();
  } catch (error) {
    console.error('Error pulling model:', error);
    throw error;
  }
};

export const getHealthStatus = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    if (!response.ok) {
      throw new Error('Failed to fetch health status');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching health status:', error);
    throw error;
  }
};

// Reports API
export const getReports = async (page = 1, limit = 20, filePath = null) => {
  try {
    const params = new URLSearchParams({ page: page.toString(), limit: limit.toString() });
    if (filePath) {
      params.append('file_path', filePath);
    }

    const response = await fetch(`${API_BASE_URL}/reports?${params}`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to fetch reports');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching reports:', error);
    throw error;
  }
};

export const getReport = async (reportId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/reports/${reportId}`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to fetch report');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching report:', error);
    throw error;
  }
};

export const deleteReport = async (reportId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/reports/${reportId}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to delete report');
    }
    return await response.json();
  } catch (error) {
    console.error('Error deleting report:', error);
    throw error;
  }
};

// Feedback API
export const addFeedback = async (reportId, feedback) => {
  try {
    const response = await fetch(`${API_BASE_URL}/reports/${reportId}/feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(feedback),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to submit feedback');
    }
    return await response.json();
  } catch (error) {
    console.error('Error submitting feedback:', error);
    throw error;
  }
};

export const getReportFeedback = async (reportId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/reports/${reportId}/feedback`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to fetch feedback');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching feedback:', error);
    throw error;
  }
};

export const getFeedbackSummary = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/reports/feedback/summary`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to fetch feedback summary');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching feedback summary:', error);
    throw error;
  }
};

export const getReportsOverview = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/reports/stats/overview`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to fetch reports overview');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching reports overview:', error);
    throw error;
  }
};

// Analytics API
export const fetchVulnerabilityTrends = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/analytics/trends/vulnerabilities`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to fetch vulnerability trends');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching vulnerability trends:', error);
    throw error;
  }
};

export const fetchModelPerformance = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/analytics/trends/model_performance`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to fetch model performance');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching model performance:', error);
    throw error;
  }
};

export const fetchReportSummary = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/analytics/reports/summary`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to fetch report summary');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching report summary:', error);
    throw error;
  }
};

export const fetchFeedbackSummary = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/analytics/feedback/summary`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to fetch feedback summary');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching feedback summary:', error);
    throw error;
  }
};

// Export API
export const exportReportPDF = async (reportId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/reports/${reportId}/export/pdf`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to export PDF');
    }
    return await response.blob();
  } catch (error) {
    console.error('Error exporting PDF:', error);
    throw error;
  }
};

export const getExportCapabilities = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/reports/export/capabilities`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to fetch export capabilities');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching export capabilities:', error);
    throw error;
  }
};
