import React, { useState, useEffect } from 'react';

const BulkOperationsManagement = () => {
  const [activeTab, setActiveTab] = useState('batch-analysis');
  const [templates, setTemplates] = useState([]);
  const [scheduledScans, setScheduledScans] = useState([]);
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Batch Analysis State
  const [batchFiles, setBatchFiles] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [llmType, setLlmType] = useState('ollama');

  // Bulk Export State
  const [exportReportIds, setExportReportIds] = useState('');
  const [exportFormat, setExportFormat] = useState('json');
  const [includeMetadata, setIncludeMetadata] = useState(true);

  // Template Creation State
  const [newTemplate, setNewTemplate] = useState({
    id: '',
    name: '',
    description: '',
    file_patterns: [],
    analysis_config: {},
    llm_type: 'ollama'
  });

  // Scheduled Scan State
  const [newScan, setNewScan] = useState({
    id: '',
    name: '',
    description: '',
    repository_url: '',
    schedule_cron: '0 2 * * *',
    is_active: true
  });

  useEffect(() => {
    fetchTemplates();
    fetchScheduledScans();
    fetchJobs();
  }, []);

  const fetchTemplates = async () => {
    try {
      const response = await fetch('/bulk/templates');
      if (response.ok) {
        const data = await response.json();
        setTemplates(data.templates);
      }
    } catch (err) {
      console.error('Failed to fetch templates:', err);
    }
  };

  const fetchScheduledScans = async () => {
    try {
      const response = await fetch('/bulk/scans');
      if (response.ok) {
        const data = await response.json();
        setScheduledScans(data.scans);
      }
    } catch (err) {
      console.error('Failed to fetch scheduled scans:', err);
    }
  };

  const fetchJobs = async () => {
    try {
      const response = await fetch('/performance/jobs/active');
      if (response.ok) {
        const data = await response.json();
        setJobs(data.active_jobs);
      }
    } catch (err) {
      console.error('Failed to fetch jobs:', err);
    }
  };

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    const filePromises = files.map(file => {
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          resolve({
            path: file.name,
            content: e.target.result
          });
        };
        reader.readAsText(file);
      });
    });

    Promise.all(filePromises).then(fileData => {
      setBatchFiles(fileData);
    });
  };

  const submitBatchAnalysis = async () => {
    if (batchFiles.length === 0) {
      setError('Please select files for analysis');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/bulk/analysis/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          files: batchFiles,
          analysis_config: {},
          llm_type: llmType,
          template_id: selectedTemplate || null
        }),
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Batch analysis submitted! Job ID: ${result.job_id}`);
        setBatchFiles([]);
        fetchJobs();
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to submit batch analysis');
      }
    } catch (err) {
      setError('Error submitting batch analysis');
    } finally {
      setLoading(false);
    }
  };

  const submitBulkExport = async () => {
    if (!exportReportIds.trim()) {
      setError('Please enter report IDs for export');
      return;
    }

    setLoading(true);
    try {
      const reportIds = exportReportIds.split(',').map(id => id.trim());
      
      const response = await fetch('/bulk/export/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_ids: reportIds,
          export_format: exportFormat,
          include_metadata: includeMetadata,
          include_thought_process: true,
          compression: false
        }),
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Bulk export submitted! Job ID: ${result.job_id}`);
        setExportReportIds('');
        fetchJobs();
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to submit bulk export');
      }
    } catch (err) {
      setError('Error submitting bulk export');
    } finally {
      setLoading(false);
    }
  };

  const createTemplate = async () => {
    if (!newTemplate.id || !newTemplate.name) {
      setError('Please fill in template ID and name');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/bulk/templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...newTemplate,
          file_patterns: newTemplate.file_patterns.length > 0 ? newTemplate.file_patterns : ['*.py']
        }),
      });

      if (response.ok) {
        alert('Template created successfully!');
        setNewTemplate({
          id: '',
          name: '',
          description: '',
          file_patterns: [],
          analysis_config: {},
          llm_type: 'ollama'
        });
        fetchTemplates();
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to create template');
      }
    } catch (err) {
      setError('Error creating template');
    } finally {
      setLoading(false);
    }
  };

  const createScheduledScan = async () => {
    if (!newScan.id || !newScan.name) {
      setError('Please fill in scan ID and name');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/bulk/scans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newScan),
      });

      if (response.ok) {
        alert('Scheduled scan created successfully!');
        setNewScan({
          id: '',
          name: '',
          description: '',
          repository_url: '',
          schedule_cron: '0 2 * * *',
          is_active: true
        });
        fetchScheduledScans();
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to create scheduled scan');
      }
    } catch (err) {
      setError('Error creating scheduled scan');
    } finally {
      setLoading(false);
    }
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Bulk Operations & Automation</h2>
        <p className="text-gray-600">Manage batch analysis, bulk exports, templates, and scheduled scans</p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
          <button 
            onClick={() => setError(null)}
            className="ml-2 text-red-500 hover:text-red-700"
          >
            ×
          </button>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'batch-analysis', label: 'Batch Analysis' },
            { id: 'bulk-export', label: 'Bulk Export' },
            { id: 'templates', label: 'Templates' },
            { id: 'scheduled-scans', label: 'Scheduled Scans' },
            { id: 'jobs', label: 'Active Jobs' }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Batch Analysis Tab */}
      {activeTab === 'batch-analysis' && (
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">Batch Code Analysis</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Upload Files
                </label>
                <input
                  type="file"
                  multiple
                  accept=".py,.js,.jsx,.ts,.tsx,.java,.go,.php,.rb,.cpp,.c,.h"
                  onChange={handleFileUpload}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
                {batchFiles.length > 0 && (
                  <p className="mt-2 text-sm text-gray-600">
                    {batchFiles.length} files selected
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Analysis Template
                  </label>
                  <select
                    value={selectedTemplate}
                    onChange={(e) => setSelectedTemplate(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Default Analysis</option>
                    {templates.map(template => (
                      <option key={template.id} value={template.id}>
                        {template.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    LLM Type
                  </label>
                  <select
                    value={llmType}
                    onChange={(e) => setLlmType(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="ollama">Ollama</option>
                    <option value="openai">OpenAI</option>
                  </select>
                </div>
              </div>

              <button
                onClick={submitBatchAnalysis}
                disabled={loading || batchFiles.length === 0}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {loading ? 'Submitting...' : 'Start Batch Analysis'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Bulk Export Tab */}
      {activeTab === 'bulk-export' && (
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">Bulk Report Export</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Report IDs (comma-separated)
                </label>
                <textarea
                  value={exportReportIds}
                  onChange={(e) => setExportReportIds(e.target.value)}
                  placeholder="report_1, report_2, report_3"
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  rows="3"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Export Format
                  </label>
                  <select
                    value={exportFormat}
                    onChange={(e) => setExportFormat(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="json">JSON</option>
                    <option value="pdf">PDF</option>
                    <option value="csv">CSV</option>
                    <option value="xlsx">Excel</option>
                  </select>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="include_metadata"
                    checked={includeMetadata}
                    onChange={(e) => setIncludeMetadata(e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="include_metadata" className="text-sm font-medium text-gray-700">
                    Include Metadata
                  </label>
                </div>
              </div>

              <button
                onClick={submitBulkExport}
                disabled={loading || !exportReportIds.trim()}
                className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
              >
                {loading ? 'Submitting...' : 'Start Bulk Export'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Templates Tab */}
      {activeTab === 'templates' && (
        <div className="space-y-6">
          {/* Create Template Form */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">Create Analysis Template</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Template ID</label>
                <input
                  type="text"
                  value={newTemplate.id}
                  onChange={(e) => setNewTemplate({...newTemplate, id: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="my_custom_template"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Template Name</label>
                <input
                  type="text"
                  value={newTemplate.name}
                  onChange={(e) => setNewTemplate({...newTemplate, name: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="My Custom Template"
                />
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={newTemplate.description}
                onChange={(e) => setNewTemplate({...newTemplate, description: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                rows="3"
                placeholder="Template description..."
              />
            </div>

            <button
              onClick={createTemplate}
              disabled={loading}
              className="mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {loading ? 'Creating...' : 'Create Template'}
            </button>
          </div>

          {/* Templates List */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">Available Templates</h3>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Patterns</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {templates.map((template) => (
                    <tr key={template.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{template.name}</div>
                        <div className="text-sm text-gray-500">{template.id}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">{template.description}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">
                          {template.file_patterns.join(', ')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDateTime(template.created_at)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Scheduled Scans Tab */}
      {activeTab === 'scheduled-scans' && (
        <div className="space-y-6">
          {/* Create Scheduled Scan Form */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">Create Scheduled Scan</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Scan ID</label>
                <input
                  type="text"
                  value={newScan.id}
                  onChange={(e) => setNewScan({...newScan, id: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="daily_security_scan"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Scan Name</label>
                <input
                  type="text"
                  value={newScan.name}
                  onChange={(e) => setNewScan({...newScan, name: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="Daily Security Scan"
                />
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Repository URL</label>
              <input
                type="url"
                value={newScan.repository_url}
                onChange={(e) => setNewScan({...newScan, repository_url: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="https://github.com/user/repo"
              />
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Schedule (Cron)</label>
              <input
                type="text"
                value={newScan.schedule_cron}
                onChange={(e) => setNewScan({...newScan, schedule_cron: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="0 2 * * *"
              />
              <p className="mt-1 text-sm text-gray-500">Daily at 2 AM: 0 2 * * *</p>
            </div>

            <button
              onClick={createScheduledScan}
              disabled={loading}
              className="mt-4 bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
            >
              {loading ? 'Creating...' : 'Create Scheduled Scan'}
            </button>
          </div>

          {/* Scheduled Scans List */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">Scheduled Scans</h3>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Repository</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Next Run</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {scheduledScans.map((scan) => (
                    <tr key={scan.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{scan.name}</div>
                        <div className="text-sm text-gray-500">{scan.id}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">{scan.repository_url || 'N/A'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {scan.schedule_cron}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          scan.is_active ? 'text-green-800 bg-green-100' : 'text-red-800 bg-red-100'
                        }`}>
                          {scan.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDateTime(scan.next_run)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Active Jobs Tab */}
      {activeTab === 'jobs' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold">Active Jobs</h3>
              <button
                onClick={fetchJobs}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Refresh
              </button>
            </div>
            
            {jobs.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                No active jobs
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job ID</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Task</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Worker</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Args</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {jobs.map((job) => (
                      <tr key={job.job_id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {job.job_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {job.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {job.worker}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {JSON.stringify(job.args).substring(0, 100)}...
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default BulkOperationsManagement;
