import React from 'react';
import { Bar } from 'react-chartjs-2';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
} from 'chart.js';

ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend
);

const ModelPerformanceChart = ({ data }) => {
    const chartData = {
        labels: data.map(d => d.model_name),
        datasets: [
            {
                label: 'Accuracy',
                data: data.map(d => d.accuracy),
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                borderColor: 'rgb(75, 192, 192)',
                borderWidth: 1,
            },
            {
                label: 'Precision',
                data: data.map(d => d.precision),
                backgroundColor: 'rgba(153, 102, 255, 0.5)',
                borderColor: 'rgb(153, 102, 255)',
                borderWidth: 1,
            },
            {
                label: 'Recall',
                data: data.map(d => d.recall),
                backgroundColor: 'rgba(255, 159, 64, 0.5)',
                borderColor: 'rgb(255, 159, 64)',
                borderWidth: 1,
            },
            {
                label: 'F1-Score',
                data: data.map(d => d.f1_score),
                backgroundColor: 'rgba(255, 99, 132, 0.5)',
                borderColor: 'rgb(255, 99, 132)',
                borderWidth: 1,
            },
        ],
    };

    const options = {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: true,
                text: 'Model Performance Metrics',
            },
        },
        scales: {
            y: {
                beginAtZero: true,
                max: 1.0,
                title: {
                    display: true,
                    text: 'Score',
                },
            },
            x: {
                title: {
                    display: true,
                    text: 'Model',
                },
            },
        },
    };

    return <Bar data={chartData} options={options} />;
};

export default ModelPerformanceChart;