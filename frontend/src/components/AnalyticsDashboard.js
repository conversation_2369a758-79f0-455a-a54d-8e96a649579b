import React, { useState, useEffect } from 'react';
import { fetchVulnerabilityTrends, fetchModelPerformance, fetchReportSummary, fetchFeedbackSummary } from '../services/api';
import VulnerabilityTrendsChart from './VulnerabilityTrendsChart';
import ModelPerformanceChart from './ModelPerformanceChart';
import CustomReportTemplates from './CustomReportTemplates';
import ExportAnalytics from './ExportAnalytics';

const AnalyticsDashboard = () => {
    const [vulnerabilityTrends, setVulnerabilityTrends] = useState([]);
    const [modelPerformance, setModelPerformance] = useState([]);
    const [reportSummary, setReportSummary] = useState({});
    const [feedbackSummary, setFeedbackSummary] = useState({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const [
                    vTrends,
                    mPerformance,
                    rSummary,
                    fSummary
                ] = await Promise.all([
                    fetchVulnerabilityTrends(),
                    fetchModelPerformance(),
                    fetchReportSummary(),
                    fetchFeedbackSummary()
                ]);
                setVulnerabilityTrends(vTrends);
                setModelPerformance(mPerformance);
                setReportSummary(rSummary);
                setFeedbackSummary(fSummary);
            } catch (err) {
                setError('Failed to fetch analytics data.');
                console.error('Error fetching analytics data:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    if (loading) return <div>Loading analytics data...</div>;
    if (error) return <div className="text-red-500">{error}</div>;

    return (
        <div className="p-6 bg-gray-100 min-h-screen">
            <h1 className="text-3xl font-bold text-gray-800 mb-6">Advanced Analytics & Reporting</h1>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="bg-white p-4 rounded-lg shadow">
                    <h2 className="text-xl font-semibold text-gray-700 mb-4">Vulnerability Trends</h2>
                    <VulnerabilityTrendsChart data={vulnerabilityTrends} />
                </div>
                <div className="bg-white p-4 rounded-lg shadow">
                    <h2 className="text-xl font-semibold text-gray-700 mb-4">Model Performance</h2>
                    <ModelPerformanceChart data={modelPerformance} />
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="bg-white p-4 rounded-lg shadow">
                    <h2 className="text-xl font-semibold text-gray-700 mb-4">Report Summary</h2>
                    <pre className="bg-gray-50 p-3 rounded text-sm overflow-auto">
                        {JSON.stringify(reportSummary, null, 2)}
                    </pre>
                </div>
                <div className="bg-white p-4 rounded-lg shadow">
                    <h2 className="text-xl font-semibold text-gray-700 mb-4">Feedback Summary</h2>
                    <pre className="bg-gray-50 p-3 rounded text-sm overflow-auto">
                        {JSON.stringify(feedbackSummary, null, 2)}
                    </pre>
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white p-4 rounded-lg shadow">
                    <h2 className="text-xl font-semibold text-gray-700 mb-4">Custom Report Templates</h2>
                    <CustomReportTemplates />
                </div>
                <div className="bg-white p-4 rounded-lg shadow">
                    <h2 className="text-xl font-semibold text-gray-700 mb-4">Export Analytics</h2>
                    <ExportAnalytics />
                </div>
            </div>
        </div>
    );
};

export default AnalyticsDashboard;