### Implementation Plan: Real-time Updates & WebSocket Integration

**Goal:** To enable real-time communication between the backend and frontend for live analysis progress, notifications, and dashboard updates.

**Current State:** A basic WebSocket endpoint `/ws` exists in `backend/app/main.py` that echoes received messages.

**What remains to be done:**
1.  **Backend:**
    *   Enhance the existing WebSocket endpoint to handle different types of real-time messages.
    *   Implement a broadcasting mechanism to send messages to all connected clients or specific clients.
    *   Integrate real-time updates into the Deep Reasoning Protocol to stream analysis progress.
    *   Develop a notification system for critical vulnerabilities and other events.
    *   Implement logic to push dashboard updates (e.g., new reports, updated statistics).
2.  **Frontend:**
    *   Establish and manage WebSocket connections.
    *   Implement client-side logic to receive and interpret different message types.
    *   Update UI components dynamically based on real-time data (progress bars, notifications, dashboard refreshes).

**Implementation Plan Details:**

#### Phase 1: Backend WebSocket Enhancements

1.  **Refactor WebSocket Endpoint:**
    *   Move WebSocket handling logic into a dedicated service (e.g., `backend/app/services/websocket_service.py`) to keep `main.py` clean.
    *   Define clear message formats (e.g., JSON with a `type` field and `payload`).

2.  **Implement Broadcasting Mechanism:**
    *   Use an in-memory list of active WebSocket connections for simplicity initially. For scalability, consider integrating a message broker like Redis Pub/Sub.
    *   Create a `broadcast_message` function in the WebSocket service.

3.  **Integrate Analysis Progress Updates:**
    *   Modify the `DeepReasoningProtocol` in `backend/app/services/deep_reasoning_protocol.py` to emit WebSocket messages at the completion of each phase.
    *   These messages will contain the current phase, status, and any relevant intermediate results.

4.  **Develop Notification System:**
    *   Define a message type for notifications (e.g., `{"type": "notification", "payload": {"severity": "critical", "message": "SQL Injection found!"}}`).
    *   Integrate notification emission into the vulnerability reporting process.

5.  **Dashboard Update Triggers:**
    *   When a new report is saved or analytics data changes, trigger a WebSocket message to update the frontend dashboard.

#### Phase 2: Frontend WebSocket Integration

1.  **Create WebSocket Service/Hook:**
    *   Develop a React hook or a dedicated service (e.g., `frontend/src/services/websocket.js`) to manage the WebSocket connection.
    *   Handle connection lifecycle (open, close, error, reconnect).

2.  **Implement Event Listeners:**
    *   In `App.js` or relevant dashboard components, subscribe to WebSocket messages.
    *   Use a `switch` statement or a mapping to handle different message `type`s.

3.  **Dynamic UI Updates:**
    *   **Analysis Progress:** Display a progress bar or step-by-step log in the analysis view, updating as messages are received.
    *   **Notifications:** Show toast notifications or a dedicated notification area for critical alerts.
    *   **Dashboard Refresh:** Automatically re-fetch data for `AnalyticsDashboard.js`, `ReportsOverview.js`, etc., when a "dashboard_update" message is received.

**Anticipated Dependencies and Challenges:**

*   **Dependencies:**
    *   `websockets` library for FastAPI (already present).
    *   Frontend WebSocket API (built-in browser support).
    *   (Optional for scalability) Redis for Pub/Sub.
*   **Challenges:**
    *   **Connection Management:** Ensuring stable WebSocket connections, especially across network changes or backend restarts. Implementing robust reconnect logic.
    *   **Message Serialization/Deserialization:** Consistent JSON formatting between backend and frontend.
    *   **State Management:** Effectively updating React component state based on real-time data without causing excessive re-renders.
    *   **Scalability:** For a large number of concurrent users, an in-memory solution might not suffice, necessitating a message broker.
    *   **Security:** Ensuring WebSocket connections are secure (WSS, origin checks, authentication if multi-user is implemented).

**Mermaid Diagram: High-Level Architecture**

```mermaid
graph TD
    User(User) -->|Sends Code| Frontend(Frontend Application)
    Frontend -->|HTTP POST /analyze| Backend(FastAPI Backend)
    Backend -->|Processes Analysis| DeepReasoningProtocol(Deep Reasoning Protocol)
    DeepReasoningProtocol -->|Emits Progress Updates| WebSocketService(WebSocket Service)
    Backend -->|Sends Notifications/Updates| WebSocketService
    WebSocketService -->|Broadcasts Messages| Frontend
    Frontend -->|Displays Real-time Data| User
```

**Mermaid Diagram: Backend WebSocket Flow**

```mermaid
sequenceDiagram
    participant F as Frontend
    participant B as Backend (main.py)
    participant WS as WebSocketService
    participant DRP as DeepReasoningProtocol
    participant DB as Database/Analytics

    F->>B: Establish WebSocket Connection (/ws)
    B->>WS: Register Client Connection
    loop Analysis Phases
        DRP->>WS: Send Progress Update (e.g., "Phase 1 Complete")
        WS->>F: Broadcast Progress Message
    end
    DRP->>DB: Save Vulnerability Report
    DB->>WS: Trigger Dashboard Update
    WS->>F: Broadcast Dashboard Update Message
    DB->>WS: Trigger Notification (e.g., Critical Vuln Found)
    WS->>F: Broadcast Notification Message
    F->>B: Send Chat Message (if implemented)
    B->>WS: Process Chat Message
    WS->>F: Broadcast Chat Message