#!/usr/bin/env python3
"""
Test Performance and Scalability Enhancements

This script tests the performance and scalability features:
- Enhanced cache service
- Background job processing
- Database service
- Performance monitoring
"""

import sys
import asyncio
import time
from datetime import datetime

# Add the backend directory to the path
sys.path.append('.')

def test_imports():
    """Test that all performance modules can be imported"""
    try:
        print("Testing performance enhancement imports...")
        
        # Test enhanced cache service
        from app.services.enhanced_cache_service import EnhancedCacheService, get_enhanced_cache_service
        print("✅ Enhanced cache service imported successfully")
        
        # Test background jobs service
        from app.services.background_jobs import BackgroundJobService, get_background_job_service
        print("✅ Background jobs service imported successfully")
        
        # Test database service
        from app.services.database_service import DatabaseService, get_database_service
        print("✅ Database service imported successfully")
        
        # Test performance monitoring
        from app.services.performance_monitoring import PerformanceMonitor, get_performance_monitor
        print("✅ Performance monitoring imported successfully")
        
        # Test configuration
        from app.core.performance_config import PerformanceConfig, get_performance_config
        print("✅ Performance configuration imported successfully")
        
        # Test API endpoints
        from app.api.performance import router
        print("✅ Performance API endpoints imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def test_enhanced_cache():
    """Test enhanced cache service functionality"""
    try:
        print("\nTesting Enhanced Cache Service...")
        
        from app.services.enhanced_cache_service import EnhancedCacheService
        
        # Create cache service (will use memory cache if Redis not available)
        cache = EnhancedCacheService(redis_url=None)  # Force memory cache for testing
        
        # Test basic operations
        await cache.set("test_key", {"message": "Hello, Cache!"}, ttl=60)
        print("✅ Cache set operation successful")
        
        value = await cache.get("test_key")
        assert value == {"message": "Hello, Cache!"}, "Cache value mismatch"
        print("✅ Cache get operation successful")
        
        exists = await cache.exists("test_key")
        assert exists, "Cache key should exist"
        print("✅ Cache exists check successful")
        
        # Test cache key generation
        key = cache._generate_key("test_prefix", param1="value1", param2="value2")
        assert "test_prefix" in key, "Cache key should contain prefix"
        print("✅ Cache key generation successful")
        
        # Test statistics
        stats = await cache.get_stats()
        assert "hits" in stats, "Stats should contain hits"
        print("✅ Cache statistics successful")
        
        # Cleanup
        await cache.delete("test_key")
        await cache.close()
        
        print("✅ Enhanced cache service test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced cache test failed: {e}")
        return False

async def test_background_jobs():
    """Test background job service functionality"""
    try:
        print("\nTesting Background Jobs Service...")
        
        from app.services.background_jobs import BackgroundJobService
        
        # Create job service (will use sync fallback if Celery not available)
        job_service = BackgroundJobService()
        
        # Test job submission (will run synchronously if Celery not available)
        job_id = await job_service.submit_job("test_task", "arg1", "arg2", kwarg1="value1")
        assert job_id is not None, "Job ID should not be None"
        print("✅ Job submission successful")
        
        # Test job result retrieval
        result = await job_service.get_job_result(job_id)
        assert result.job_id == job_id, "Job ID should match"
        print("✅ Job result retrieval successful")
        
        # Test job statistics
        stats = await job_service.get_job_stats()
        assert "celery_available" in stats, "Stats should contain celery_available"
        print("✅ Job statistics successful")
        
        # Test active jobs
        active_jobs = await job_service.get_active_jobs()
        assert isinstance(active_jobs, list), "Active jobs should be a list"
        print("✅ Active jobs retrieval successful")
        
        print("✅ Background jobs service test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Background jobs test failed: {e}")
        return False

async def test_database_service():
    """Test database service functionality"""
    try:
        print("\nTesting Database Service...")
        
        from app.services.database_service import DatabaseService
        
        # Create database service (will handle missing dependencies gracefully)
        db_service = DatabaseService()
        
        # Test connection (will fail gracefully if PostgreSQL not available)
        try:
            connection_ok = await db_service.test_connection()
            print(f"✅ Database connection test completed (Connected: {connection_ok})")
        except Exception as e:
            print(f"⚠️  Database connection test failed (expected if PostgreSQL not configured): {e}")
        
        # Test statistics
        stats = await db_service.get_database_stats()
        assert hasattr(stats, 'total_connections'), "Stats should have total_connections"
        print("✅ Database statistics successful")
        
        # Test query execution (will fail gracefully if no connection)
        try:
            result = await db_service.execute_query("SELECT 1 as test")
            print("✅ Database query execution successful")
        except Exception as e:
            print(f"⚠️  Database query failed (expected if PostgreSQL not configured): {e}")
        
        # Cleanup
        await db_service.close()
        
        print("✅ Database service test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database service test failed: {e}")
        return False

def test_performance_monitoring():
    """Test performance monitoring functionality"""
    try:
        print("\nTesting Performance Monitoring...")
        
        from app.services.performance_monitoring import PerformanceMonitor
        
        # Create performance monitor
        monitor = PerformanceMonitor(collection_interval=1, retention_hours=1)
        
        # Test metrics collection
        system_metrics = monitor._collect_system_metrics()
        assert system_metrics.cpu_percent >= 0, "CPU percent should be non-negative"
        print("✅ System metrics collection successful")
        
        app_metrics = monitor._collect_app_metrics()
        assert hasattr(app_metrics, 'request_count'), "App metrics should have request_count"
        print("✅ Application metrics collection successful")
        
        # Test request tracking
        monitor.track_request(0.5, is_error=False)
        monitor.track_request(1.2, is_error=True)
        monitor.track_llm_request(2.5)
        print("✅ Request tracking successful")
        
        # Test current metrics
        current = monitor.get_current_metrics()
        assert "monitoring_active" in current, "Current metrics should contain monitoring_active"
        print("✅ Current metrics retrieval successful")
        
        # Test metrics history
        history = monitor.get_metrics_history(hours=1)
        assert "system" in history and "application" in history, "History should contain system and application"
        print("✅ Metrics history retrieval successful")
        
        # Test metrics export
        exported = monitor.export_metrics("json")
        assert isinstance(exported, str), "Exported metrics should be a string"
        print("✅ Metrics export successful")
        
        print("✅ Performance monitoring test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Performance monitoring test failed: {e}")
        return False

def test_performance_config():
    """Test performance configuration"""
    try:
        print("\nTesting Performance Configuration...")
        
        from app.core.performance_config import PerformanceConfig, get_performance_config
        
        # Test configuration loading
        config = get_performance_config()
        assert hasattr(config, 'cache'), "Config should have cache settings"
        assert hasattr(config, 'database'), "Config should have database settings"
        assert hasattr(config, 'jobs'), "Config should have job settings"
        assert hasattr(config, 'monitoring'), "Config should have monitoring settings"
        assert hasattr(config, 'scaling'), "Config should have scaling settings"
        print("✅ Configuration loading successful")
        
        # Test configuration to dict
        config_dict = config.to_dict()
        assert "cache" in config_dict, "Config dict should contain cache"
        assert "database" in config_dict, "Config dict should contain database"
        print("✅ Configuration serialization successful")
        
        # Test environment validation
        from app.core.performance_config import validate_environment
        validate_environment()  # Should not raise an exception
        print("✅ Environment validation successful")
        
        print("✅ Performance configuration test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Performance configuration test failed: {e}")
        return False

async def run_performance_tests():
    """Run all performance enhancement tests"""
    print("=" * 60)
    print("Performance & Scalability Enhancements - Test Suite")
    print("=" * 60)
    
    tests = [
        ("Import Tests", test_imports),
        ("Enhanced Cache", test_enhanced_cache),
        ("Background Jobs", test_background_jobs),
        ("Database Service", test_database_service),
        ("Performance Monitoring", test_performance_monitoring),
        ("Performance Configuration", test_performance_config)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
                
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All performance enhancement tests passed!")
        print("\nPerformance & Scalability Enhancements are ready for use!")
        print("\nFeatures available:")
        print("- ✅ Enhanced caching with Redis support")
        print("- ✅ Background job processing with Celery")
        print("- ✅ Database optimization with PostgreSQL")
        print("- ✅ Performance monitoring and alerting")
        print("- ✅ Configurable scaling parameters")
        print("- ✅ REST API endpoints for management")
        return True
    else:
        print("⚠️  Some tests failed. Check the implementation.")
        print("\nNote: Some failures may be expected if external services")
        print("(Redis, PostgreSQL, Celery) are not configured.")
        return False

if __name__ == "__main__":
    # Run the test suite
    success = asyncio.run(run_performance_tests())
    sys.exit(0 if success else 1)
