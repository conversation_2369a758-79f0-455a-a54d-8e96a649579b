"""
Bulk Operations API Endpoints

This module provides REST API endpoints for:
- Batch analysis of multiple files
- Bulk export operations
- Analysis template management
- Scheduled scan management
- Workflow automation
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, UploadFile, File
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime

from backend.app.services.bulk_operations_service import (
    get_bulk_operations_service,
    BatchAnalysisRequest,
    BulkExportRequest,
    AnalysisTemplate,
    ScheduledScan
)

router = APIRouter(prefix="/bulk", tags=["Bulk Operations & Automation"])

# Global service instance
bulk_service = get_bulk_operations_service()


# Request/Response Models

class BatchAnalysisRequestModel(BaseModel):
    """Batch analysis request model"""
    files: List[Dict[str, str]]  # [{"path": "file.py", "content": "code..."}]
    analysis_config: Dict[str, Any] = {}
    llm_type: str = "ollama"
    priority: str = "medium"
    notification_webhook: Optional[str] = None
    export_format: str = "json"
    template_id: Optional[str] = None


class BulkExportRequestModel(BaseModel):
    """Bulk export request model"""
    report_ids: List[str]
    export_format: str  # json, pdf, csv, xlsx
    include_metadata: bool = True
    include_thought_process: bool = True
    compression: bool = False
    notification_webhook: Optional[str] = None


class AnalysisTemplateModel(BaseModel):
    """Analysis template model"""
    id: str
    name: str
    description: str
    file_patterns: List[str]
    analysis_config: Dict[str, Any]
    llm_type: str = "ollama"
    custom_prompts: Optional[Dict[str, str]] = None
    validation_rules: Optional[Dict[str, Any]] = None
    export_settings: Optional[Dict[str, Any]] = None
    created_by: str = "user"


class ScheduledScanModel(BaseModel):
    """Scheduled scan model"""
    id: str
    name: str
    description: str
    repository_url: Optional[str] = None
    file_patterns: Optional[List[str]] = None
    schedule_cron: str = "0 2 * * *"
    llm_type: str = "ollama"
    template_id: Optional[str] = None
    notification_webhook: Optional[str] = None
    is_active: bool = True


class JobStatusResponse(BaseModel):
    """Job status response model"""
    job_id: str
    status: str
    progress: int = 0
    result: Optional[Any] = None
    error: Optional[str] = None


# Batch Analysis Endpoints

@router.post("/analysis/submit", response_model=JobStatusResponse)
async def submit_batch_analysis(request: BatchAnalysisRequestModel):
    """Submit a batch analysis job"""
    try:
        batch_request = BatchAnalysisRequest(
            files=request.files,
            analysis_config=request.analysis_config,
            llm_type=request.llm_type,
            priority=request.priority,
            notification_webhook=request.notification_webhook,
            export_format=request.export_format,
            template_id=request.template_id
        )
        
        job_id = await bulk_service.submit_batch_analysis(batch_request)
        
        return JobStatusResponse(
            job_id=job_id,
            status="SUBMITTED",
            progress=0
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to submit batch analysis: {str(e)}")


@router.get("/analysis/{job_id}", response_model=JobStatusResponse)
async def get_batch_analysis_status(job_id: str):
    """Get the status of a batch analysis job"""
    try:
        result = await bulk_service.get_batch_analysis_result(job_id)
        
        return JobStatusResponse(
            job_id=result.job_id,
            status=result.status,
            progress=result.progress,
            result=result.result,
            error=result.error
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get batch analysis status: {str(e)}")


@router.post("/analysis/upload")
async def upload_files_for_analysis(
    files: List[UploadFile] = File(...),
    llm_type: str = "ollama",
    template_id: Optional[str] = None
):
    """Upload files for batch analysis"""
    try:
        file_data = []
        
        for file in files:
            content = await file.read()
            file_data.append({
                "path": file.filename,
                "content": content.decode('utf-8')
            })
        
        batch_request = BatchAnalysisRequest(
            files=file_data,
            analysis_config={},
            llm_type=llm_type,
            template_id=template_id
        )
        
        job_id = await bulk_service.submit_batch_analysis(batch_request)
        
        return {
            "message": f"Uploaded {len(files)} files for analysis",
            "job_id": job_id,
            "files": [f.filename for f in files]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to upload files: {str(e)}")


# Bulk Export Endpoints

@router.post("/export/submit", response_model=JobStatusResponse)
async def submit_bulk_export(request: BulkExportRequestModel):
    """Submit a bulk export job"""
    try:
        export_request = BulkExportRequest(
            report_ids=request.report_ids,
            export_format=request.export_format,
            include_metadata=request.include_metadata,
            include_thought_process=request.include_thought_process,
            compression=request.compression,
            notification_webhook=request.notification_webhook
        )
        
        job_id = await bulk_service.submit_bulk_export(export_request)
        
        return JobStatusResponse(
            job_id=job_id,
            status="SUBMITTED",
            progress=0
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to submit bulk export: {str(e)}")


@router.get("/export/{job_id}")
async def get_bulk_export_status(job_id: str):
    """Get the status of a bulk export job"""
    try:
        result = await bulk_service.get_batch_analysis_result(job_id)  # Same service method
        
        return {
            "job_id": result.job_id,
            "status": result.status,
            "progress": result.progress,
            "result": result.result,
            "error": result.error
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get bulk export status: {str(e)}")


# Template Management Endpoints

@router.get("/templates")
async def list_templates():
    """List all analysis templates"""
    try:
        templates = bulk_service.list_templates()
        return {
            "templates": [
                {
                    "id": t.id,
                    "name": t.name,
                    "description": t.description,
                    "file_patterns": t.file_patterns,
                    "llm_type": t.llm_type,
                    "created_by": t.created_by,
                    "created_at": t.created_at.isoformat() if t.created_at else None
                }
                for t in templates
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list templates: {str(e)}")


@router.post("/templates")
async def create_template(template: AnalysisTemplateModel):
    """Create a new analysis template"""
    try:
        analysis_template = AnalysisTemplate(
            id=template.id,
            name=template.name,
            description=template.description,
            file_patterns=template.file_patterns,
            analysis_config=template.analysis_config,
            llm_type=template.llm_type,
            custom_prompts=template.custom_prompts,
            validation_rules=template.validation_rules,
            export_settings=template.export_settings,
            created_by=template.created_by
        )
        
        success = bulk_service.create_template(analysis_template)
        
        if success:
            return {"message": f"Template '{template.name}' created successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to create template")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create template: {str(e)}")


@router.get("/templates/{template_id}")
async def get_template(template_id: str):
    """Get a specific analysis template"""
    try:
        template = bulk_service.get_template(template_id)
        
        if not template:
            raise HTTPException(status_code=404, detail="Template not found")
        
        return {
            "id": template.id,
            "name": template.name,
            "description": template.description,
            "file_patterns": template.file_patterns,
            "analysis_config": template.analysis_config,
            "llm_type": template.llm_type,
            "custom_prompts": template.custom_prompts,
            "validation_rules": template.validation_rules,
            "export_settings": template.export_settings,
            "created_by": template.created_by,
            "created_at": template.created_at.isoformat() if template.created_at else None,
            "updated_at": template.updated_at.isoformat() if template.updated_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get template: {str(e)}")


@router.put("/templates/{template_id}")
async def update_template(template_id: str, updates: Dict[str, Any]):
    """Update an analysis template"""
    try:
        success = bulk_service.update_template(template_id, updates)
        
        if success:
            return {"message": f"Template '{template_id}' updated successfully"}
        else:
            raise HTTPException(status_code=404, detail="Template not found")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update template: {str(e)}")


@router.delete("/templates/{template_id}")
async def delete_template(template_id: str):
    """Delete an analysis template"""
    try:
        success = bulk_service.delete_template(template_id)
        
        if success:
            return {"message": f"Template '{template_id}' deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="Template not found")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete template: {str(e)}")


# Scheduled Scan Endpoints

@router.get("/scans")
async def list_scheduled_scans():
    """List all scheduled scans"""
    try:
        scans = bulk_service.list_scheduled_scans()
        return {
            "scans": [
                {
                    "id": s.id,
                    "name": s.name,
                    "description": s.description,
                    "repository_url": s.repository_url,
                    "schedule_cron": s.schedule_cron,
                    "is_active": s.is_active,
                    "last_run": s.last_run.isoformat() if s.last_run else None,
                    "next_run": s.next_run.isoformat() if s.next_run else None
                }
                for s in scans
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list scheduled scans: {str(e)}")


@router.post("/scans")
async def create_scheduled_scan(scan: ScheduledScanModel):
    """Create a new scheduled scan"""
    try:
        scheduled_scan = ScheduledScan(
            id=scan.id,
            name=scan.name,
            description=scan.description,
            repository_url=scan.repository_url,
            file_patterns=scan.file_patterns,
            schedule_cron=scan.schedule_cron,
            llm_type=scan.llm_type,
            template_id=scan.template_id,
            notification_webhook=scan.notification_webhook,
            is_active=scan.is_active
        )
        
        success = bulk_service.create_scheduled_scan(scheduled_scan)
        
        if success:
            return {"message": f"Scheduled scan '{scan.name}' created successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to create scheduled scan")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create scheduled scan: {str(e)}")


@router.post("/scans/check")
async def check_scheduled_scans(background_tasks: BackgroundTasks):
    """Check and execute due scheduled scans"""
    try:
        # Run scan check in background
        background_tasks.add_task(bulk_service.check_scheduled_scans)
        
        return {"message": "Scheduled scan check initiated"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to check scheduled scans: {str(e)}")


@router.get("/scans/{scan_id}")
async def get_scheduled_scan(scan_id: str):
    """Get a specific scheduled scan"""
    try:
        scan = bulk_service.get_scheduled_scan(scan_id)
        
        if not scan:
            raise HTTPException(status_code=404, detail="Scheduled scan not found")
        
        return {
            "id": scan.id,
            "name": scan.name,
            "description": scan.description,
            "repository_url": scan.repository_url,
            "file_patterns": scan.file_patterns,
            "schedule_cron": scan.schedule_cron,
            "llm_type": scan.llm_type,
            "template_id": scan.template_id,
            "notification_webhook": scan.notification_webhook,
            "is_active": scan.is_active,
            "last_run": scan.last_run.isoformat() if scan.last_run else None,
            "next_run": scan.next_run.isoformat() if scan.next_run else None,
            "created_at": scan.created_at.isoformat() if scan.created_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get scheduled scan: {str(e)}")


@router.put("/scans/{scan_id}")
async def update_scheduled_scan(scan_id: str, updates: Dict[str, Any]):
    """Update a scheduled scan"""
    try:
        success = bulk_service.update_scheduled_scan(scan_id, updates)
        
        if success:
            return {"message": f"Scheduled scan '{scan_id}' updated successfully"}
        else:
            raise HTTPException(status_code=404, detail="Scheduled scan not found")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update scheduled scan: {str(e)}")


@router.delete("/scans/{scan_id}")
async def delete_scheduled_scan(scan_id: str):
    """Delete a scheduled scan"""
    try:
        success = bulk_service.delete_scheduled_scan(scan_id)
        
        if success:
            return {"message": f"Scheduled scan '{scan_id}' deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="Scheduled scan not found")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete scheduled scan: {str(e)}")


# Utility Endpoints

@router.get("/stats")
async def get_bulk_operations_stats():
    """Get bulk operations statistics"""
    try:
        templates = bulk_service.list_templates()
        scans = bulk_service.list_scheduled_scans()
        
        return {
            "templates": {
                "total": len(templates),
                "by_type": {}  # Could categorize by file patterns
            },
            "scheduled_scans": {
                "total": len(scans),
                "active": len([s for s in scans if s.is_active]),
                "inactive": len([s for s in scans if not s.is_active])
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get bulk operations stats: {str(e)}")
