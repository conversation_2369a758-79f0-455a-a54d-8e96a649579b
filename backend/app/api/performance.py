"""
Performance and Scalability API Endpoints

This module provides REST API endpoints for:
- Performance monitoring and metrics
- Cache management and statistics
- Background job management
- Database optimization and statistics
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime

from backend.app.services.enhanced_cache_service import get_enhanced_cache_service
from backend.app.services.background_jobs import get_background_job_service
from backend.app.services.database_service import get_database_service
from backend.app.services.performance_monitoring import get_performance_monitor

router = APIRouter(prefix="/performance", tags=["Performance & Scalability"])

# Global service instances
cache_service = get_enhanced_cache_service()
job_service = get_background_job_service()
db_service = get_database_service()
perf_monitor = get_performance_monitor()


# Request/Response Models

class CacheStatsResponse(BaseModel):
    """Cache statistics response"""
    hits: int
    misses: int
    hit_rate: float
    total_size: int
    redis_available: bool


class JobSubmissionRequest(BaseModel):
    """Background job submission request"""
    task_name: str
    args: List[Any] = []
    kwargs: Dict[str, Any] = {}


class JobStatusResponse(BaseModel):
    """Background job status response"""
    job_id: str
    status: str
    result: Optional[Any] = None
    error: Optional[str] = None
    progress: int = 0


class DatabaseStatsResponse(BaseModel):
    """Database statistics response"""
    total_connections: int
    active_connections: int
    idle_connections: int
    total_queries: int
    average_query_time: float
    database_size: Optional[str] = None


class PerformanceMetricsResponse(BaseModel):
    """Performance metrics response"""
    system: Optional[Dict[str, Any]] = None
    application: Optional[Dict[str, Any]] = None
    monitoring_active: bool
    collection_interval: int


# Cache Management Endpoints

@router.get("/cache/stats", response_model=CacheStatsResponse)
async def get_cache_stats():
    """Get cache performance statistics"""
    try:
        stats = await cache_service.get_stats()
        return CacheStatsResponse(
            hits=stats.get('hits', 0),
            misses=stats.get('misses', 0),
            hit_rate=stats.get('hit_rate', 0.0),
            total_size=stats.get('total_size', 0),
            redis_available=stats.get('redis_available', False)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get cache stats: {str(e)}")


@router.post("/cache/clear")
async def clear_cache():
    """Clear all cached data"""
    try:
        success = await cache_service.clear()
        if success:
            return {"message": "Cache cleared successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to clear cache")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to clear cache: {str(e)}")


@router.delete("/cache/{key}")
async def delete_cache_key(key: str):
    """Delete a specific cache key"""
    try:
        success = await cache_service.delete(key)
        if success:
            return {"message": f"Cache key '{key}' deleted successfully"}
        else:
            return {"message": f"Cache key '{key}' not found"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete cache key: {str(e)}")


@router.get("/cache/{key}/exists")
async def check_cache_key_exists(key: str):
    """Check if a cache key exists"""
    try:
        exists = await cache_service.exists(key)
        return {"key": key, "exists": exists}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to check cache key: {str(e)}")


# Background Job Management Endpoints

@router.post("/jobs/submit", response_model=JobStatusResponse)
async def submit_background_job(request: JobSubmissionRequest):
    """Submit a background job for processing"""
    try:
        job_id = await job_service.submit_job(request.task_name, *request.args, **request.kwargs)
        return JobStatusResponse(
            job_id=job_id,
            status="SUBMITTED",
            progress=0
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to submit job: {str(e)}")


@router.get("/jobs/{job_id}", response_model=JobStatusResponse)
async def get_job_status(job_id: str):
    """Get the status of a background job"""
    try:
        result = await job_service.get_job_result(job_id)
        return JobStatusResponse(
            job_id=result.job_id,
            status=result.status,
            result=result.result,
            error=result.error,
            progress=result.progress
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get job status: {str(e)}")


@router.delete("/jobs/{job_id}")
async def cancel_job(job_id: str):
    """Cancel a background job"""
    try:
        success = await job_service.cancel_job(job_id)
        if success:
            return {"message": f"Job {job_id} cancelled successfully"}
        else:
            return {"message": f"Failed to cancel job {job_id}"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to cancel job: {str(e)}")


@router.get("/jobs/active")
async def get_active_jobs():
    """Get list of active background jobs"""
    try:
        jobs = await job_service.get_active_jobs()
        return {"active_jobs": jobs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get active jobs: {str(e)}")


@router.get("/jobs/stats")
async def get_job_stats():
    """Get background job processing statistics"""
    try:
        stats = await job_service.get_job_stats()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get job stats: {str(e)}")


# Database Management Endpoints

@router.get("/database/stats", response_model=DatabaseStatsResponse)
async def get_database_stats():
    """Get database performance statistics"""
    try:
        stats = await db_service.get_database_stats()
        return DatabaseStatsResponse(
            total_connections=stats.total_connections,
            active_connections=stats.active_connections,
            idle_connections=stats.idle_connections,
            total_queries=stats.total_queries,
            average_query_time=stats.average_query_time,
            database_size=stats.database_size
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get database stats: {str(e)}")


@router.post("/database/optimize")
async def optimize_database(background_tasks: BackgroundTasks):
    """Run database optimization tasks"""
    try:
        # Run optimization in background
        background_tasks.add_task(db_service.optimize_database)
        return {"message": "Database optimization started in background"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start database optimization: {str(e)}")


@router.post("/database/backup")
async def create_database_backup(background_tasks: BackgroundTasks, backup_path: Optional[str] = None):
    """Create a database backup"""
    try:
        # Run backup in background
        background_tasks.add_task(db_service.create_backup, backup_path)
        return {"message": "Database backup started in background"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start database backup: {str(e)}")


@router.get("/database/slow-queries")
async def get_slow_queries(limit: int = 10):
    """Get slow database queries"""
    try:
        queries = await db_service.get_slow_queries(limit)
        return {"slow_queries": queries}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get slow queries: {str(e)}")


@router.get("/database/test-connection")
async def test_database_connection():
    """Test database connection"""
    try:
        success = await db_service.test_connection()
        return {"connection_ok": success}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database connection test failed: {str(e)}")


# Performance Monitoring Endpoints

@router.get("/metrics/current", response_model=PerformanceMetricsResponse)
async def get_current_metrics():
    """Get current performance metrics"""
    try:
        metrics = perf_monitor.get_current_metrics()
        return PerformanceMetricsResponse(**metrics)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get current metrics: {str(e)}")


@router.get("/metrics/history")
async def get_metrics_history(hours: int = 1):
    """Get performance metrics history"""
    try:
        if hours < 1 or hours > 24:
            raise HTTPException(status_code=400, detail="Hours must be between 1 and 24")
        
        history = perf_monitor.get_metrics_history(hours)
        return history
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get metrics history: {str(e)}")


@router.post("/monitoring/start")
async def start_monitoring():
    """Start performance monitoring"""
    try:
        perf_monitor.start_monitoring()
        return {"message": "Performance monitoring started"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start monitoring: {str(e)}")


@router.post("/monitoring/stop")
async def stop_monitoring():
    """Stop performance monitoring"""
    try:
        perf_monitor.stop_monitoring()
        return {"message": "Performance monitoring stopped"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to stop monitoring: {str(e)}")


@router.get("/monitoring/export")
async def export_metrics(format: str = "json"):
    """Export performance metrics"""
    try:
        if format not in ["json", "csv"]:
            raise HTTPException(status_code=400, detail="Format must be 'json' or 'csv'")
        
        exported_data = perf_monitor.export_metrics(format)
        
        return {
            "format": format,
            "data": exported_data,
            "exported_at": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to export metrics: {str(e)}")


# System Health Check Endpoint

@router.get("/health")
async def system_health_check():
    """Comprehensive system health check"""
    try:
        health_status = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "services": {}
        }
        
        # Check cache service
        try:
            cache_stats = await cache_service.get_stats()
            health_status["services"]["cache"] = {
                "status": "healthy",
                "redis_available": cache_stats.get("redis_available", False),
                "hit_rate": cache_stats.get("hit_rate", 0.0)
            }
        except Exception as e:
            health_status["services"]["cache"] = {"status": "unhealthy", "error": str(e)}
            health_status["overall_status"] = "degraded"
        
        # Check database service
        try:
            db_ok = await db_service.test_connection()
            health_status["services"]["database"] = {
                "status": "healthy" if db_ok else "unhealthy",
                "connection_ok": db_ok
            }
            if not db_ok:
                health_status["overall_status"] = "unhealthy"
        except Exception as e:
            health_status["services"]["database"] = {"status": "unhealthy", "error": str(e)}
            health_status["overall_status"] = "unhealthy"
        
        # Check job service
        try:
            job_stats = await job_service.get_job_stats()
            health_status["services"]["jobs"] = {
                "status": "healthy",
                "celery_available": job_stats.get("celery_available", False)
            }
        except Exception as e:
            health_status["services"]["jobs"] = {"status": "degraded", "error": str(e)}
        
        # Check monitoring service
        health_status["services"]["monitoring"] = {
            "status": "healthy",
            "active": perf_monitor.monitoring_active
        }
        
        return health_status
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")
