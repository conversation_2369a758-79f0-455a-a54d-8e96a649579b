from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Security, Depends, Request, status, WebSocket, WebSocketDisconnect
from backend.app.services.websocket_manager import WebSocketManager
from backend.app.models.websocket_message import WebSocketMessage # New import
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime
from rq import Queue
from redis import Redis
from contextlib import asynccontextmanager
import os
import logging
import json
import traceback
import time
from collections import defaultdict
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from starlette.types import AS<PERSON>App
from sqlalchemy.orm import Session # Added for dependency injection
from datetime import date # Added for date type in query parameters

from backend.llm.llm_integration import LLMIntegration
from backend.app.services.deep_reasoning_protocol import DeepReasoningProtocol
from backend.app.services.rag_service import RAGService
from backend.app.models.vulnerability_report import VulnerabilityReport, VulnerabilityDetail
from backend.app.models.code_analysis_request import CodeAnalysisRequest
from backend.app.api.reports import router as reports_router
from backend.app.api.knowledge_sources import router as knowledge_sources_router
from backend.app.api.performance import router as performance_router
from backend.app.api.bulk_operations import router as bulk_operations_router
from backend.app.db.models import Base as DBBase
from backend.app.db.database import get_db, create_db_and_tables
from backend.app.services.analytics_service import AnalyticsService
from backend.app.models.analytics import (
    VulnerabilityScanEvent,
    ModelPerformanceEvent,
    UserFeedbackEvent,
    AnalyticsSummary,
)
from backend.app.tasks.analytics_tasks import generate_and_export_report_task

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    create_db_and_tables()
    print("Database tables created and verified.")
    yield
    # Shutdown (if needed) - e.g., close database connections if not handled by FastAPI

app = FastAPI(lifespan=lifespan)

# Initialize WebSocketManager
websocket_manager = WebSocketManager()

# Configure structured logging
class JsonFormatter(logging.Formatter):
    def format(self, record):
        log_record = {
            "timestamp": self.formatTime(record, self.datefmt),
            "level": record.levelname,
            "name": record.name,
            "message": record.getMessage(),
            "pathname": record.pathname,
            "lineno": record.lineno,
        }
        if record.exc_info:
            log_record["exc_info"] = self.formatException(record.exc_info)
        return json.dumps(log_record)

# Remove default handlers to prevent duplicate logs
logging.getLogger().handlers.clear()

# Configure root logger
logger = logging.getLogger()
logger.setLevel(os.getenv("LOG_LEVEL", "INFO").upper())

# Console handler
console_handler = logging.StreamHandler()
console_handler.setFormatter(JsonFormatter())
logger.addHandler(console_handler)

# Example of adding a file handler (optional, for production)
# file_handler = logging.FileHandler("app.log")
# file_handler.setFormatter(JsonFormatter())
# logger.addHandler(file_handler)

logger.info("Application starting up...")

# Include routers
app.include_router(reports_router)
app.include_router(knowledge_sources_router)
app.include_router(performance_router)
app.include_router(bulk_operations_router)

# Analytics Endpoints
@app.post("/analytics/events/vulnerability_scan", status_code=status.HTTP_201_CREATED)
async def record_vulnerability_scan(
    event: VulnerabilityScanEvent, db: Session = Depends(get_db)
):
    service = AnalyticsService(db)
    service.record_vulnerability_scan_event(event)
    return {"message": "Vulnerability scan event recorded successfully."}

@app.post("/analytics/events/model_performance", status_code=status.HTTP_201_CREATED)
async def record_model_performance(
    event: ModelPerformanceEvent, db: Session = Depends(get_db)
):
    service = AnalyticsService(db)
    service.record_model_performance_event(event)
    return {"message": "Model performance event recorded successfully."}

@app.post("/analytics/events/user_feedback", status_code=status.HTTP_201_CREATED)
async def record_user_feedback(
    event: UserFeedbackEvent, db: Session = Depends(get_db)
):
    service = AnalyticsService(db)
    service.record_user_feedback_event(event)
    return {"message": "User feedback event recorded successfully."}

@app.get("/analytics/trends/vulnerabilities", response_model=List[Dict])
async def get_vulnerability_trends(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    interval: str = "daily",
    llm_type: Optional[str] = None,
    db: Session = Depends(get_db),
):
    service = AnalyticsService(db)
    trends = service.get_vulnerability_trends(start_date, end_date, interval, llm_type)
    return trends

@app.get("/analytics/trends/model_performance", response_model=List[Dict])
async def get_model_performance_trends(
    model_name: Optional[str] = None,
    llm_type: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    metric: Optional[str] = None,
    db: Session = Depends(get_db),
):
    service = AnalyticsService(db)
    trends = service.get_model_performance_trends(
        model_name, llm_type, start_date, end_date, metric
    )
    return trends

@app.get("/analytics/reports/summary", response_model=AnalyticsSummary)
async def get_analytics_summary(db: Session = Depends(get_db)):
    service = AnalyticsService(db)
    summary = service.get_analytics_summary()
    return summary

@app.get("/analytics/feedback/summary")
async def get_feedback_summary(db: Session = Depends(get_db)):
    service = AnalyticsService(db)
    summary = service.get_feedback_summary()
    return summary

class ScheduleReportRequest(BaseModel):
    report_type: str
    user_id: str
    schedule_time: Optional[datetime] = None # For immediate or specific time scheduling

@app.post("/analytics/reports/schedule", status_code=status.HTTP_202_ACCEPTED)
async def schedule_analytics_report(request: ScheduleReportRequest):
    """
    Schedules an analytics report to be generated in the background.
    If schedule_time is provided, it schedules for that time. Otherwise, it enqueues immediately.
    """
    if request.schedule_time:
        # Schedule the job for a specific time
        job = q.enqueue_at(
            request.schedule_time,
            generate_and_export_report_task,
            request.report_type,
            request.user_id
        )
        message = f"Report '{request.report_type}' for user '{request.user_id}' scheduled to run at {request.schedule_time} with job ID: {job.id}"
    else:
        # Enqueue the job immediately
        job = q.enqueue(
            generate_and_export_report_task,
            request.report_type,
            request.user_id
        )
        message = f"Report '{request.report_type}' for user '{request.user_id}' enqueued immediately with job ID: {job.id}"
    
    logger.info(message)
    return {"message": message, "job_id": job.id}

# Configure CORS
# Load origins from environment variable, with a fallback for development
ALLOWED_ORIGINS = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000,http://localhost").split(',')

# Initialize Redis and RQ Queue
redis_conn = Redis(host='redis', port=6379, db=0)
q = Queue(connection=redis_conn)

app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize LLM and RAG services globally or using FastAPI's dependency injection
llm_integration = LLMIntegration()
rag_service = RAGService()

class AnalyzeResponse(BaseModel):
    thought_process_log: str
    final_vulnerability_report: VulnerabilityReport

@app.get("/health")
async def health_check():
    return {"status": "ok"}
# Load SECRET_TOKEN from environment variable
SECRET_TOKEN = os.getenv("SECRET_TOKEN", "your-super-secret-token-default") # Provide a default for development, but ensure it's overridden in production

security = HTTPBearer()

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    token = credentials.credentials
    if token != SECRET_TOKEN:
        logger.warning("Authentication failed: Invalid credentials provided.")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
    logger.info("User authenticated successfully.")
    return "authenticated_user" # In a real app, this would return user details

# In-memory store for rate limiting
request_counts = defaultdict(lambda: {"count": 0, "last_reset_time": 0})
RATE_LIMIT_WINDOW = 60  # seconds
MAX_REQUESTS = 10     # requests per window

class RateLimitMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        if request.url.path == "/analyze":
            client_ip = request.client.host
            current_time = time.time()

            if current_time - request_counts[client_ip]["last_reset_time"] > RATE_LIMIT_WINDOW:
                request_counts[client_ip]["count"] = 0
                request_counts[client_ip]["last_reset_time"] = current_time

            if request_counts[client_ip]["count"] >= MAX_REQUESTS:
                logger.warning(f"Rate limit exceeded for IP: {client_ip}")
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={"message": "Rate limit exceeded. Please try again later."},
                )
            
            request_counts[client_ip]["count"] += 1
            logger.info(f"Rate limit check for IP {client_ip}: {request_counts[client_ip]['count']}/{MAX_REQUESTS} requests in {RATE_LIMIT_WINDOW} seconds.")

        response = await call_next(request)
        return response

app.add_middleware(RateLimitMiddleware)

@app.post("/analyze", response_model=AnalyzeResponse)
async def analyze_code_endpoint(request: CodeAnalysisRequest, current_user: str = Depends(get_current_user)):
    """
    Analyzes the provided code for vulnerabilities using the Deep Reasoning Protocol.
    Accepts either a code snippet directly or content from an uploaded file.
    """
    logger.info(f"Received analysis request for file: {request.file_path}, user: {current_user}")
    drp = DeepReasoningProtocol(llm_integration)
    
    code_to_analyze = request.uploaded_file_content if request.uploaded_file_content else request.code

    if not code_to_analyze:
        logger.error("No code provided for analysis.")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No code provided for analysis. Please provide code or upload a file.")

    logger.info(f"Code snippet length for {request.file_path}: {len(code_to_analyze)} characters")

    report = await drp.analyze_code(code_to_analyze, request.file_path, request.llm_type)

    if report:
        # For now, we'll return a placeholder thought process.
        # The actual thought process would be captured during the DRP execution.
        # This needs to be refined to capture the actual thought process from the LLM.
        thought_process_log = "Deep Reasoning Protocol executed. Thought process logging to be integrated."

        # Record vulnerability scan event using the new analytics service
        try:
            db_session = next(get_db()) # Manually get a DB session for this part
            analytics_service_instance = AnalyticsService(db=db_session)
            scan_event = VulnerabilityScanEvent(
                scan_id=report.report_id, # Assuming report_id can be used as scan_id
                timestamp=report.timestamp,
                file_path=report.file_path,
                total_vulnerabilities_found=len(report.vulnerabilities),
                severity_distribution={
                    sev: sum(1 for v in report.vulnerabilities if v.severity == sev)
                    for sev in ["Critical", "High", "Medium", "Low", "Informational"]
                },
                llm_type=request.llm_type,
                scan_duration_seconds=0.0, # Placeholder, needs actual calculation
                analysis_summary=report.analysis_summary,
            )
            analytics_service_instance.record_vulnerability_scan_event(scan_event)
            logger.info(f"Vulnerability scan event recorded for report ID: {report.report_id}")
        except Exception as e:
            logger.warning(f"Could not record vulnerability scan event: {e}", exc_info=True)
        finally:
            if 'db_session' in locals() and db_session:
                db_session.close()

        # Add the analyzed code to the RAG database for future retrieval
        try:
            rag_service.add_documents(
                documents=[code_to_analyze],
                metadatas=[{"file_path": request.file_path, "analysis_summary": report.analysis_summary}],
                ids=[f"{request.file_path}_{report.timestamp}"] # Unique ID for the document
            )
            logger.info(f"Code from {request.file_path} added to RAG database.")
        except Exception as e:
            logger.warning(f"Could not add document to RAG service: {e}", exc_info=True)
            # Do not raise HTTPException, as core analysis is complete

        return AnalyzeResponse(
            thought_process_log=thought_process_log,
            final_vulnerability_report=report
        )
    else:
        logger.error("Deep Reasoning Protocol failed to generate a valid vulnerability report.")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to generate a valid vulnerability report.")

@app.get("/models/list")
async def list_llm_models(model_type: str = "ollama"):
    """
    Returns a list of available LLM models from the specified LLM service.
    """
    try:
        models = llm_integration.get_available_models(llm_type=model_type)
        logger.info(f"Successfully listed LLM models for type: {model_type}")
        return {"models": models}
    except ValueError as e:
        logger.error(f"Bad request for listing LLM models: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid model type provided.")
    except Exception as e:
        logger.exception(f"Failed to retrieve LLM models from {model_type}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve LLM models. Please try again later.")

class ModelPullRequest(BaseModel):
    model_name: str
    model_type: str = "ollama"

@app.post("/models/pull")
async def pull_llm_model(request: ModelPullRequest):
    """
    Initiates a pull operation for a specified LLM model.
    Currently supports Ollama models.
    """
    if request.model_type.lower() == "ollama":
        try:
            llm_integration.pull_ollama_model(request.model_name)
            logger.info(f"Successfully initiated pull for Ollama model: {request.model_name}")
            return {"status": "success", "message": f"Successfully initiated pull for model {request.model_name} from Ollama."}
        except Exception as e:
            logger.exception(f"Failed to pull Ollama model {request.model_name}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to pull LLM model. Please try again later.")
    elif request.model_type.lower() == "lm_studio":
        logger.warning("Attempted to pull LM Studio model via endpoint, which is not supported.")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Model pulling is not directly supported for LM Studio via this endpoint as it typically uses local models. Please manage LM Studio models directly within LM Studio.")
    else:
        logger.error(f"Unsupported LLM type for pulling: {request.model_type}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Unsupported LLM type for pulling: {request.model_type}")

# Custom Exception Handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    logger.error(f"HTTP Exception: {exc.status_code} - {exc.detail}", exc_info=True)
    return JSONResponse(
        status_code=exc.status_code,
        content={"message": exc.detail},
    )

@app.exception_handler(Exception)
async def generic_exception_handler(request: Request, exc: Exception):
    logger.critical(f"Unhandled Exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"message": "An unexpected error occurred. Please try again later."},
    )

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket_manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_json()
            message = WebSocketMessage(type="client_message", payload=data)
            await websocket_manager.broadcast(message.model_dump_json())
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)
        logger.info(f"Client {websocket.client.host}:{websocket.client.port} disconnected from WebSocket.")
    except Exception as e:
        logger.error(f"WebSocket error for client {websocket.client.host}:{websocket.client.port}: {e}", exc_info=True)
