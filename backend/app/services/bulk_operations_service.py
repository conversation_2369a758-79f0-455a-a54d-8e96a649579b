"""
Bulk Operations Service

This service provides:
- Batch analysis of multiple files/repositories
- Scheduled automated scans
- Bulk export operations
- Template management for analysis workflows
- Automation and integration hooks
"""

import os
import json
import logging
import asyncio
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import zipfile
import tempfile

from backend.app.services.background_jobs import get_background_job_service, JobResult
from backend.app.services.enhanced_cache_service import get_enhanced_cache_service
from backend.app.services.deep_reasoning_protocol import DeepReasoningProtocol
from backend.llm.llm_integration import LLMIntegration

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class BatchAnalysisRequest:
    """Request for batch analysis operation"""
    files: List[Dict[str, str]]  # [{"path": "file.py", "content": "code..."}]
    analysis_config: Dict[str, Any]
    llm_type: str = "ollama"
    priority: str = "medium"  # low, medium, high
    notification_webhook: Optional[str] = None
    export_format: str = "json"  # json, pdf, csv
    template_id: Optional[str] = None


@dataclass
class ScheduledScan:
    """Scheduled scan configuration"""
    id: str
    name: str
    description: str
    repository_url: Optional[str] = None
    file_patterns: List[str] = None  # ["*.py", "*.js"]
    schedule_cron: str = "0 2 * * *"  # Daily at 2 AM
    llm_type: str = "ollama"
    template_id: Optional[str] = None
    notification_webhook: Optional[str] = None
    is_active: bool = True
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    created_at: datetime = None
    updated_at: datetime = None


@dataclass
class AnalysisTemplate:
    """Analysis template configuration"""
    id: str
    name: str
    description: str
    file_patterns: List[str]  # File types this template applies to
    analysis_config: Dict[str, Any]
    llm_type: str = "ollama"
    custom_prompts: Dict[str, str] = None  # Phase-specific prompts
    validation_rules: Dict[str, Any] = None
    export_settings: Dict[str, Any] = None
    created_by: str = "system"
    created_at: datetime = None
    updated_at: datetime = None


@dataclass
class BulkExportRequest:
    """Request for bulk export operation"""
    report_ids: List[str]
    export_format: str  # json, pdf, csv, xlsx
    include_metadata: bool = True
    include_thought_process: bool = True
    compression: bool = False  # Create ZIP archive
    notification_webhook: Optional[str] = None


class BulkOperationsService:
    """Service for bulk operations and automation"""
    
    def __init__(self):
        """Initialize the bulk operations service"""
        self.job_service = get_background_job_service()
        self.cache_service = get_enhanced_cache_service()
        
        # Storage for templates and scheduled scans
        self.templates: Dict[str, AnalysisTemplate] = {}
        self.scheduled_scans: Dict[str, ScheduledScan] = {}
        
        # Initialize with default templates
        self._initialize_default_templates()
        
        logger.info("Bulk Operations Service initialized")

    def _initialize_default_templates(self):
        """Initialize default analysis templates"""
        default_templates = [
            AnalysisTemplate(
                id="python_web_app",
                name="Python Web Application",
                description="Template for analyzing Python web applications",
                file_patterns=["*.py"],
                analysis_config={
                    "focus_areas": ["sql_injection", "xss", "authentication", "authorization"],
                    "security_frameworks": ["django", "flask", "fastapi"],
                    "depth_level": "comprehensive"
                },
                custom_prompts={
                    "phase_1": "Focus on web application security patterns including SQL injection, XSS, and authentication flaws.",
                    "phase_2": "Analyze data flow between web routes, database queries, and user input validation."
                },
                created_at=datetime.now()
            ),
            AnalysisTemplate(
                id="javascript_frontend",
                name="JavaScript Frontend",
                description="Template for analyzing JavaScript frontend applications",
                file_patterns=["*.js", "*.jsx", "*.ts", "*.tsx"],
                analysis_config={
                    "focus_areas": ["xss", "csrf", "dom_manipulation", "api_security"],
                    "frameworks": ["react", "vue", "angular"],
                    "depth_level": "standard"
                },
                custom_prompts={
                    "phase_1": "Focus on client-side security vulnerabilities including XSS, CSRF, and insecure API calls.",
                    "phase_2": "Analyze DOM manipulation, event handlers, and data validation on the client side."
                },
                created_at=datetime.now()
            ),
            AnalysisTemplate(
                id="api_security",
                name="API Security Analysis",
                description="Template for analyzing REST APIs and microservices",
                file_patterns=["*.py", "*.js", "*.go", "*.java"],
                analysis_config={
                    "focus_areas": ["authentication", "authorization", "input_validation", "rate_limiting"],
                    "api_types": ["rest", "graphql", "grpc"],
                    "depth_level": "comprehensive"
                },
                custom_prompts={
                    "phase_1": "Focus on API security including authentication, authorization, and input validation.",
                    "phase_2": "Analyze API endpoints, request/response handling, and security middleware."
                },
                created_at=datetime.now()
            ),
            AnalysisTemplate(
                id="infrastructure_code",
                name="Infrastructure as Code",
                description="Template for analyzing infrastructure and deployment code",
                file_patterns=["*.tf", "*.yml", "*.yaml", "Dockerfile", "*.sh"],
                analysis_config={
                    "focus_areas": ["secrets_management", "access_control", "network_security", "container_security"],
                    "tools": ["terraform", "ansible", "docker", "kubernetes"],
                    "depth_level": "comprehensive"
                },
                custom_prompts={
                    "phase_1": "Focus on infrastructure security including secrets management, access control, and network configuration.",
                    "phase_2": "Analyze deployment scripts, container configurations, and infrastructure permissions."
                },
                created_at=datetime.now()
            )
        ]
        
        for template in default_templates:
            self.templates[template.id] = template

    async def submit_batch_analysis(self, request: BatchAnalysisRequest) -> str:
        """Submit a batch analysis job"""
        try:
            # Validate request
            if not request.files:
                raise ValueError("No files provided for analysis")
            
            # Get template if specified
            template = None
            if request.template_id:
                template = self.templates.get(request.template_id)
                if not template:
                    raise ValueError(f"Template {request.template_id} not found")
            
            # Prepare job data
            job_data = {
                "files": request.files,
                "analysis_config": request.analysis_config,
                "llm_type": request.llm_type,
                "template": asdict(template) if template else None,
                "export_format": request.export_format,
                "notification_webhook": request.notification_webhook
            }
            
            # Submit background job
            job_id = await self.job_service.submit_job(
                "batch_analysis_task",
                **job_data
            )
            
            logger.info(f"Submitted batch analysis job {job_id} for {len(request.files)} files")
            return job_id
            
        except Exception as e:
            logger.error(f"Failed to submit batch analysis: {e}")
            raise

    async def get_batch_analysis_result(self, job_id: str) -> JobResult:
        """Get the result of a batch analysis job"""
        return await self.job_service.get_job_result(job_id)

    async def submit_bulk_export(self, request: BulkExportRequest) -> str:
        """Submit a bulk export job"""
        try:
            # Validate request
            if not request.report_ids:
                raise ValueError("No report IDs provided for export")
            
            # Prepare job data
            job_data = {
                "report_ids": request.report_ids,
                "export_format": request.export_format,
                "include_metadata": request.include_metadata,
                "include_thought_process": request.include_thought_process,
                "compression": request.compression,
                "notification_webhook": request.notification_webhook
            }
            
            # Submit background job
            job_id = await self.job_service.submit_job(
                "bulk_export_task",
                **job_data
            )
            
            logger.info(f"Submitted bulk export job {job_id} for {len(request.report_ids)} reports")
            return job_id
            
        except Exception as e:
            logger.error(f"Failed to submit bulk export: {e}")
            raise

    # Template Management
    
    def create_template(self, template: AnalysisTemplate) -> bool:
        """Create a new analysis template"""
        try:
            template.created_at = datetime.now()
            template.updated_at = datetime.now()
            self.templates[template.id] = template
            
            logger.info(f"Created analysis template: {template.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create template: {e}")
            return False

    def update_template(self, template_id: str, updates: Dict[str, Any]) -> bool:
        """Update an existing analysis template"""
        try:
            if template_id not in self.templates:
                return False
            
            template = self.templates[template_id]
            
            # Update fields
            for key, value in updates.items():
                if hasattr(template, key):
                    setattr(template, key, value)
            
            template.updated_at = datetime.now()
            
            logger.info(f"Updated analysis template: {template_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update template: {e}")
            return False

    def delete_template(self, template_id: str) -> bool:
        """Delete an analysis template"""
        try:
            if template_id in self.templates:
                del self.templates[template_id]
                logger.info(f"Deleted analysis template: {template_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Failed to delete template: {e}")
            return False

    def get_template(self, template_id: str) -> Optional[AnalysisTemplate]:
        """Get an analysis template by ID"""
        return self.templates.get(template_id)

    def list_templates(self) -> List[AnalysisTemplate]:
        """List all analysis templates"""
        return list(self.templates.values())

    # Scheduled Scans Management
    
    def create_scheduled_scan(self, scan: ScheduledScan) -> bool:
        """Create a new scheduled scan"""
        try:
            scan.created_at = datetime.now()
            scan.updated_at = datetime.now()
            scan.next_run = self._calculate_next_run(scan.schedule_cron)
            
            self.scheduled_scans[scan.id] = scan
            
            logger.info(f"Created scheduled scan: {scan.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create scheduled scan: {e}")
            return False

    def update_scheduled_scan(self, scan_id: str, updates: Dict[str, Any]) -> bool:
        """Update an existing scheduled scan"""
        try:
            if scan_id not in self.scheduled_scans:
                return False
            
            scan = self.scheduled_scans[scan_id]
            
            # Update fields
            for key, value in updates.items():
                if hasattr(scan, key):
                    setattr(scan, key, value)
            
            scan.updated_at = datetime.now()
            
            # Recalculate next run if schedule changed
            if 'schedule_cron' in updates:
                scan.next_run = self._calculate_next_run(scan.schedule_cron)
            
            logger.info(f"Updated scheduled scan: {scan_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update scheduled scan: {e}")
            return False

    def delete_scheduled_scan(self, scan_id: str) -> bool:
        """Delete a scheduled scan"""
        try:
            if scan_id in self.scheduled_scans:
                del self.scheduled_scans[scan_id]
                logger.info(f"Deleted scheduled scan: {scan_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Failed to delete scheduled scan: {e}")
            return False

    def get_scheduled_scan(self, scan_id: str) -> Optional[ScheduledScan]:
        """Get a scheduled scan by ID"""
        return self.scheduled_scans.get(scan_id)

    def list_scheduled_scans(self) -> List[ScheduledScan]:
        """List all scheduled scans"""
        return list(self.scheduled_scans.values())

    def _calculate_next_run(self, cron_expression: str) -> datetime:
        """Calculate next run time from cron expression"""
        # Simple implementation - in production, use croniter library
        # For now, default to daily at 2 AM
        next_run = datetime.now().replace(hour=2, minute=0, second=0, microsecond=0)
        if next_run <= datetime.now():
            next_run += timedelta(days=1)
        return next_run

    async def check_scheduled_scans(self) -> List[str]:
        """Check for scheduled scans that need to run"""
        current_time = datetime.now()
        due_scans = []
        
        for scan_id, scan in self.scheduled_scans.items():
            if (scan.is_active and 
                scan.next_run and 
                scan.next_run <= current_time):
                
                try:
                    # Submit scan job
                    job_id = await self._execute_scheduled_scan(scan)
                    due_scans.append(job_id)
                    
                    # Update last run and calculate next run
                    scan.last_run = current_time
                    scan.next_run = self._calculate_next_run(scan.schedule_cron)
                    
                except Exception as e:
                    logger.error(f"Failed to execute scheduled scan {scan_id}: {e}")
        
        return due_scans

    async def _execute_scheduled_scan(self, scan: ScheduledScan) -> str:
        """Execute a scheduled scan"""
        # This would integrate with repository scanning logic
        # For now, create a placeholder job
        job_data = {
            "scan_id": scan.id,
            "repository_url": scan.repository_url,
            "file_patterns": scan.file_patterns,
            "template_id": scan.template_id,
            "llm_type": scan.llm_type,
            "notification_webhook": scan.notification_webhook
        }
        
        job_id = await self.job_service.submit_job(
            "scheduled_scan_task",
            **job_data
        )
        
        logger.info(f"Executed scheduled scan {scan.name} as job {job_id}")
        return job_id


# Global bulk operations service instance
bulk_operations_service = None

def get_bulk_operations_service() -> BulkOperationsService:
    """Get or create the global bulk operations service instance"""
    global bulk_operations_service
    
    if bulk_operations_service is None:
        bulk_operations_service = BulkOperationsService()
    
    return bulk_operations_service
