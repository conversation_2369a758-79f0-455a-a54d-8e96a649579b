from sqlalchemy.orm import Session
from sqlalchemy import func, cast, Date
from typing import Dict, List, Optional
from datetime import datetime, date
from backend.app.services.websocket_manager import WebSocketManager

websocket_manager = WebSocketManager()

from backend.app.db.models import VulnerabilityScan, ModelPerformance, UserFeedback
from backend.app.models.analytics import (
    VulnerabilityScanEvent,
    ModelPerformanceEvent,
    UserFeedbackEvent,
    AnalyticsSummary,
)

class AnalyticsService:
    def __init__(self, db: Session):
        self.db = db

    def record_vulnerability_scan_event(self, event: VulnerabilityScanEvent):
        db_scan = VulnerabilityScan(**event.dict())
        self.db.add(db_scan)
        self.db.commit()
        self.db.refresh(db_scan)
        # Simulate a real-time update
        import asyncio
        asyncio.create_task(websocket_manager.broadcast("Analytics data updated! New vulnerability scan recorded."))
        return db_scan

    def record_model_performance_event(self, event: ModelPerformanceEvent):
        db_performance = ModelPerformance(**event.dict())
        self.db.add(db_performance)
        self.db.commit()
        self.db.refresh(db_performance)
        return db_performance

    def record_user_feedback_event(self, event: UserFeedbackEvent):
        db_feedback = UserFeedback(**event.dict())
        self.db.add(db_feedback)
        self.db.commit()
        self.db.refresh(db_feedback)
        return db_feedback

    def get_vulnerability_trends(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        interval: str = "daily",
        llm_type: Optional[str] = None,
    ) -> List[Dict]:
        query = self.db.query(VulnerabilityScan)

        if start_date:
            query = query.filter(cast(VulnerabilityScan.timestamp, Date) >= start_date)
        if end_date:
            query = query.filter(cast(VulnerabilityScan.timestamp, Date) <= end_date)
        if llm_type:
            query = query.filter(VulnerabilityScan.llm_type == llm_type)

        if interval == "daily":
            group_by_col = func.date_trunc('day', VulnerabilityScan.timestamp)
        elif interval == "weekly":
            group_by_col = func.date_trunc('week', VulnerabilityScan.timestamp)
        elif interval == "monthly":
            group_by_col = func.date_trunc('month', VulnerabilityScan.timestamp)
        else:
            raise ValueError("Invalid interval. Must be 'daily', 'weekly', or 'monthly'.")

        trends = (
            query.group_by(group_by_col)
            .order_by(group_by_col)
            .with_entities(
                group_by_col.label("date"),
                func.count(VulnerabilityScan.scan_id).label("total_scans"),
                func.sum(VulnerabilityScan.total_vulnerabilities_found).label("total_vulnerabilities"),
                func.avg(VulnerabilityScan.scan_duration_seconds).label("average_scan_duration"),
            )
            .all()
        )
        return [
            {
                "date": trend.date.isoformat(),
                "total_scans": trend.total_scans,
                "total_vulnerabilities": trend.total_vulnerabilities,
                "average_scan_duration": trend.average_scan_duration,
            }
            for trend in trends
        ]

    def get_model_performance_trends(
        self,
        model_name: Optional[str] = None,
        llm_type: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        metric: Optional[str] = None,
    ) -> List[Dict]:
        query = self.db.query(ModelPerformance)

        if model_name:
            query = query.filter(ModelPerformance.model_name == model_name)
        if llm_type:
            query = query.filter(ModelPerformance.llm_type == llm_type)
        if start_date:
            query = query.filter(cast(ModelPerformance.timestamp, Date) >= start_date)
        if end_date:
            query = query.filter(cast(ModelPerformance.timestamp, Date) <= end_date)

        # Group by day for simplicity, can be extended with interval logic
        group_by_col = func.date_trunc('day', ModelPerformance.timestamp)

        entities = [group_by_col.label("date")]
        if metric == "response_time":
            entities.append(func.avg(ModelPerformance.response_time_ms).label("average_response_time_ms"))
        elif metric == "token_usage":
            entities.append(func.sum(ModelPerformance.input_tokens).label("total_input_tokens"))
            entities.append(func.sum(ModelPerformance.output_tokens).label("total_output_tokens"))
        elif metric == "accuracy_score":
            entities.append(func.avg(ModelPerformance.accuracy_score).label("average_accuracy_score"))
        else: # Default to all metrics if not specified
            entities.append(func.avg(ModelPerformance.response_time_ms).label("average_response_time_ms"))
            entities.append(func.sum(ModelPerformance.input_tokens).label("total_input_tokens"))
            entities.append(func.sum(ModelPerformance.output_tokens).label("total_output_tokens"))
            entities.append(func.avg(ModelPerformance.accuracy_score).label("average_accuracy_score"))


        trends = (
            query.group_by(group_by_col)
            .order_by(group_by_col)
            .with_entities(*entities)
            .all()
        )

        results = []
        for trend in trends:
            trend_dict = {"date": trend.date.isoformat()}
            if hasattr(trend, "average_response_time_ms"):
                trend_dict["average_response_time_ms"] = trend.average_response_time_ms
            if hasattr(trend, "total_input_tokens"):
                trend_dict["total_input_tokens"] = trend.total_input_tokens
            if hasattr(trend, "total_output_tokens"):
                trend_dict["total_output_tokens"] = trend.total_output_tokens
            if hasattr(trend, "average_accuracy_score"):
                trend_dict["average_accuracy_score"] = trend.average_accuracy_score
            results.append(trend_dict)
        return results


    def get_analytics_summary(self) -> AnalyticsSummary:
        total_scans = self.db.query(VulnerabilityScan).count()
        total_vulnerabilities_found = self.db.query(
            func.sum(VulnerabilityScan.total_vulnerabilities_found)
        ).scalar() or 0
        average_vulnerabilities_per_scan = (
            total_vulnerabilities_found / total_scans if total_scans > 0 else 0
        )
        average_scan_duration_seconds = self.db.query(
            func.avg(VulnerabilityScan.scan_duration_seconds)
        ).scalar() or 0

        severity_distribution_overall = (
            self.db.query(
                func.jsonb_each_text(VulnerabilityScan.severity_distribution)
            )
            .all()
        )
        
        # Aggregate severity distribution from all scans
        aggregated_severity: Dict[str, int] = {}
        for row in severity_distribution_overall:
            # row is a tuple like ('Critical', '2')
            severity_key = row[0]
            severity_count = int(row[1])
            aggregated_severity[severity_key] = aggregated_severity.get(severity_key, 0) + severity_count


        return AnalyticsSummary(
            total_scans=total_scans,
            total_vulnerabilities_found=total_vulnerabilities_found,
            average_vulnerabilities_per_scan=average_vulnerabilities_per_scan,
            severity_distribution_overall=aggregated_severity,
            average_scan_duration_seconds=average_scan_duration_seconds,
        )

    def get_feedback_summary(self) -> Dict:
        total_feedback = self.db.query(UserFeedback).count()
        average_rating = self.db.query(func.avg(UserFeedback.rating)).scalar() or 0
        rating_distribution = (
            self.db.query(UserFeedback.rating, func.count(UserFeedback.rating))
            .group_by(UserFeedback.rating)
            .order_by(UserFeedback.rating)
            .all()
        )
        
        distribution_dict = {str(r): c for r, c in rating_distribution}

        return {
            "total_feedback": total_feedback,
            "average_rating": average_rating,
            "rating_distribution": distribution_dict,
        }