"""
Background Job Processing System using Celery

This service provides:
- Asynchronous task processing
- Distributed job execution
- Task scheduling and monitoring
- Retry mechanisms and error handling
- Performance monitoring
"""

import os
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
import asyncio

# Optional Celery import
try:
    from celery import Celery, Task
    from celery.result import AsyncResult
    from celery.signals import task_prerun, task_postrun, task_failure
    CELERY_AVAILABLE = True
except ImportError:
    CELERY_AVAILABLE = False
    Celery = None
    Task = None
    AsyncResult = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class JobResult:
    """Result of a background job"""
    job_id: str
    status: str  # PENDING, STARTED, SUCCESS, FAILURE, RETRY
    result: Any = None
    error: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: int = 0  # 0-100
    metadata: Dict[str, Any] = None

    @property
    def duration(self) -> Optional[timedelta]:
        """Calculate job duration"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None


class BackgroundJobService:
    """Background job processing service using Celery"""
    
    def __init__(self, 
                 broker_url: str = "redis://localhost:6379/0",
                 result_backend: str = "redis://localhost:6379/0",
                 task_routes: Optional[Dict[str, str]] = None):
        """
        Initialize the background job service
        
        Args:
            broker_url: Message broker URL (Redis/RabbitMQ)
            result_backend: Result storage backend URL
            task_routes: Custom task routing configuration
        """
        self.broker_url = broker_url
        self.result_backend = result_backend
        self.task_routes = task_routes or {}
        
        # Initialize Celery app
        self.celery_app = None
        self.celery_available = CELERY_AVAILABLE
        
        if CELERY_AVAILABLE:
            self._initialize_celery()
        else:
            logger.warning("Celery not available. Background jobs will run synchronously.")

    def _initialize_celery(self):
        """Initialize Celery application"""
        try:
            self.celery_app = Celery(
                'sentinel_trace_jobs',
                broker=self.broker_url,
                backend=self.result_backend
            )
            
            # Configure Celery
            self.celery_app.conf.update(
                task_serializer='json',
                accept_content=['json'],
                result_serializer='json',
                timezone='UTC',
                enable_utc=True,
                task_track_started=True,
                task_time_limit=30 * 60,  # 30 minutes
                task_soft_time_limit=25 * 60,  # 25 minutes
                worker_prefetch_multiplier=1,
                task_acks_late=True,
                worker_disable_rate_limits=False,
                task_routes=self.task_routes,
                result_expires=3600,  # 1 hour
            )
            
            # Register signal handlers
            self._register_signal_handlers()
            
            logger.info("Celery initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Celery: {e}")
            self.celery_available = False

    def _register_signal_handlers(self):
        """Register Celery signal handlers for monitoring"""
        
        @task_prerun.connect
        def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
            logger.info(f"Task {task.name} [{task_id}] started")

        @task_postrun.connect
        def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, retval=None, state=None, **kwds):
            logger.info(f"Task {task.name} [{task_id}] completed with state: {state}")

        @task_failure.connect
        def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwds):
            logger.error(f"Task {sender.name} [{task_id}] failed: {exception}")

    def create_task(self, func):
        """Decorator to create a Celery task"""
        if not self.celery_available:
            # Return original function if Celery not available
            return func
        
        return self.celery_app.task(bind=True)(func)

    async def submit_job(self, task_name: str, *args, **kwargs) -> str:
        """Submit a background job"""
        if not self.celery_available:
            logger.warning(f"Celery not available. Running {task_name} synchronously.")
            # Run synchronously as fallback
            try:
                result = await self._run_sync_job(task_name, *args, **kwargs)
                return f"sync_{task_name}_{datetime.now().timestamp()}"
            except Exception as e:
                logger.error(f"Sync job {task_name} failed: {e}")
                raise
        
        try:
            task = self.celery_app.send_task(task_name, args=args, kwargs=kwargs)
            logger.info(f"Submitted job {task_name} with ID: {task.id}")
            return task.id
        except Exception as e:
            logger.error(f"Failed to submit job {task_name}: {e}")
            raise

    async def get_job_result(self, job_id: str) -> JobResult:
        """Get the result of a background job"""
        if not self.celery_available or job_id.startswith("sync_"):
            # Handle sync jobs
            return JobResult(
                job_id=job_id,
                status="SUCCESS",
                result="Completed synchronously",
                completed_at=datetime.now()
            )
        
        try:
            result = AsyncResult(job_id, app=self.celery_app)
            
            job_result = JobResult(
                job_id=job_id,
                status=result.status,
                result=result.result if result.successful() else None,
                error=str(result.result) if result.failed() else None
            )
            
            # Get additional metadata if available
            if hasattr(result, 'info') and isinstance(result.info, dict):
                job_result.metadata = result.info
                job_result.progress = result.info.get('progress', 0)
            
            return job_result
            
        except Exception as e:
            logger.error(f"Failed to get job result for {job_id}: {e}")
            return JobResult(
                job_id=job_id,
                status="FAILURE",
                error=str(e)
            )

    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a background job"""
        if not self.celery_available:
            logger.warning("Cannot cancel sync jobs")
            return False
        
        try:
            self.celery_app.control.revoke(job_id, terminate=True)
            logger.info(f"Cancelled job {job_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to cancel job {job_id}: {e}")
            return False

    async def get_active_jobs(self) -> List[Dict[str, Any]]:
        """Get list of active jobs"""
        if not self.celery_available:
            return []
        
        try:
            inspect = self.celery_app.control.inspect()
            active_jobs = inspect.active()
            
            jobs = []
            for worker, tasks in (active_jobs or {}).items():
                for task in tasks:
                    jobs.append({
                        'job_id': task['id'],
                        'name': task['name'],
                        'worker': worker,
                        'args': task['args'],
                        'kwargs': task['kwargs']
                    })
            
            return jobs
            
        except Exception as e:
            logger.error(f"Failed to get active jobs: {e}")
            return []

    async def get_job_stats(self) -> Dict[str, Any]:
        """Get job processing statistics"""
        if not self.celery_available:
            return {"celery_available": False}
        
        try:
            inspect = self.celery_app.control.inspect()
            stats = inspect.stats()
            
            return {
                "celery_available": True,
                "workers": len(stats or {}),
                "worker_stats": stats
            }
            
        except Exception as e:
            logger.error(f"Failed to get job stats: {e}")
            return {"celery_available": True, "error": str(e)}

    async def _run_sync_job(self, task_name: str, *args, **kwargs) -> Any:
        """Run job synchronously as fallback"""
        # This is a placeholder - in practice, you'd import and call the actual task function
        logger.info(f"Running {task_name} synchronously with args: {args}, kwargs: {kwargs}")
        
        # Simulate some work
        await asyncio.sleep(0.1)
        
        return {"status": "completed", "task": task_name}


# Global background job service instance
background_job_service = None

def get_background_job_service(
    broker_url: str = "redis://localhost:6379/0",
    result_backend: str = "redis://localhost:6379/0"
) -> BackgroundJobService:
    """Get or create the global background job service instance"""
    global background_job_service
    
    if background_job_service is None:
        background_job_service = BackgroundJobService(
            broker_url=broker_url,
            result_backend=result_backend
        )
    
    return background_job_service


# Example task definitions
def create_analysis_tasks(job_service: BackgroundJobService):
    """Create analysis-related background tasks"""
    
    @job_service.create_task
    def analyze_code_batch(self, code_files: List[Dict[str, str]], model: str = "ollama"):
        """Analyze multiple code files in batch"""
        results = []
        total_files = len(code_files)
        
        for i, file_data in enumerate(code_files):
            try:
                # Update progress
                progress = int((i / total_files) * 100)
                self.update_state(state='PROGRESS', meta={'progress': progress, 'current': i, 'total': total_files})
                
                # Simulate analysis (replace with actual analysis logic)
                file_path = file_data.get('path', f'file_{i}')
                code = file_data.get('content', '')
                
                # Here you would call the actual analysis service
                result = {
                    'file_path': file_path,
                    'status': 'analyzed',
                    'vulnerabilities_found': 0,  # Placeholder
                    'analysis_time': 1.5  # Placeholder
                }
                
                results.append(result)
                
            except Exception as e:
                logger.error(f"Failed to analyze file {file_data.get('path', 'unknown')}: {e}")
                results.append({
                    'file_path': file_data.get('path', 'unknown'),
                    'status': 'error',
                    'error': str(e)
                })
        
        return {
            'status': 'completed',
            'total_files': total_files,
            'successful': len([r for r in results if r['status'] == 'analyzed']),
            'failed': len([r for r in results if r['status'] == 'error']),
            'results': results
        }

    @job_service.create_task
    def update_knowledge_base(self, source_ids: Optional[List[str]] = None):
        """Update knowledge base sources in background"""
        try:
            # This would call the enhanced RAG service
            from .enhanced_rag_service import EnhancedRAGService
            
            rag_service = EnhancedRAGService()
            
            if source_ids:
                results = {}
                for source_id in source_ids:
                    result = asyncio.run(rag_service.update_knowledge_base(source_id))
                    results[source_id] = result
            else:
                results = asyncio.run(rag_service.update_knowledge_base())
            
            return {
                'status': 'completed',
                'results': results
            }
            
        except Exception as e:
            logger.error(f"Knowledge base update failed: {e}")
            raise

    @job_service.create_task
    def generate_analytics_report(self, report_type: str, date_range: Dict[str, str]):
        """Generate analytics report in background"""
        try:
            # Simulate report generation
            self.update_state(state='PROGRESS', meta={'progress': 25, 'stage': 'collecting_data'})
            
            # Collect data
            await asyncio.sleep(2)
            
            self.update_state(state='PROGRESS', meta={'progress': 50, 'stage': 'processing_data'})
            
            # Process data
            await asyncio.sleep(2)
            
            self.update_state(state='PROGRESS', meta={'progress': 75, 'stage': 'generating_report'})
            
            # Generate report
            await asyncio.sleep(1)
            
            return {
                'status': 'completed',
                'report_type': report_type,
                'date_range': date_range,
                'report_url': f'/reports/{report_type}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
            }
            
        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            raise

    @job_service.create_task
    def batch_analysis_task(self, files, analysis_config, llm_type="ollama", template=None, export_format="json", notification_webhook=None):
        """Analyze multiple files in batch with optional template"""
        try:
            from .deep_reasoning_protocol import DeepReasoningProtocol
            from ..llm.llm_integration import LLMIntegration

            results = []
            total_files = len(files)

            # Initialize analysis components
            llm_integration = LLMIntegration()
            drp = DeepReasoningProtocol(llm_integration)

            for i, file_data in enumerate(files):
                try:
                    # Update progress
                    progress = int((i / total_files) * 100)
                    self.update_state(state='PROGRESS', meta={
                        'progress': progress,
                        'current': i,
                        'total': total_files,
                        'current_file': file_data.get('path', f'file_{i}')
                    })

                    file_path = file_data.get('path', f'file_{i}')
                    code = file_data.get('content', '')

                    # Apply template customizations if provided
                    if template:
                        # Customize analysis based on template
                        custom_prompts = template.get('custom_prompts', {})
                        # This would modify the DRP prompts based on template

                    # Perform analysis
                    report = asyncio.run(drp.analyze_code(code, file_path, llm_type))

                    result = {
                        'file_path': file_path,
                        'status': 'analyzed',
                        'report': report.dict() if report else None,
                        'analysis_time': 2.5  # Placeholder
                    }

                    results.append(result)

                except Exception as e:
                    logger.error(f"Failed to analyze file {file_data.get('path', 'unknown')}: {e}")
                    results.append({
                        'file_path': file_data.get('path', 'unknown'),
                        'status': 'error',
                        'error': str(e)
                    })

            # Generate export if requested
            export_data = None
            if export_format != "json":
                export_data = self._generate_export(results, export_format)

            # Send webhook notification if provided
            if notification_webhook:
                self._send_webhook_notification(notification_webhook, {
                    'type': 'batch_analysis_complete',
                    'total_files': total_files,
                    'successful': len([r for r in results if r['status'] == 'analyzed']),
                    'failed': len([r for r in results if r['status'] == 'error'])
                })

            return {
                'status': 'completed',
                'total_files': total_files,
                'successful': len([r for r in results if r['status'] == 'analyzed']),
                'failed': len([r for r in results if r['status'] == 'error']),
                'results': results,
                'export_data': export_data
            }

        except Exception as e:
            logger.error(f"Batch analysis failed: {e}")
            raise

    @job_service.create_task
    def bulk_export_task(self, report_ids, export_format, include_metadata=True, include_thought_process=True, compression=False, notification_webhook=None):
        """Export multiple reports in bulk"""
        try:
            import json
            import zipfile
            import tempfile
            from datetime import datetime

            exported_reports = []
            total_reports = len(report_ids)

            for i, report_id in enumerate(report_ids):
                try:
                    # Update progress
                    progress = int((i / total_reports) * 100)
                    self.update_state(state='PROGRESS', meta={
                        'progress': progress,
                        'current': i,
                        'total': total_reports,
                        'current_report': report_id
                    })

                    # Load report (this would load from actual storage)
                    report_data = self._load_report(report_id)

                    if report_data:
                        # Format report based on export format
                        formatted_report = self._format_report_for_export(
                            report_data,
                            export_format,
                            include_metadata,
                            include_thought_process
                        )

                        exported_reports.append({
                            'report_id': report_id,
                            'status': 'exported',
                            'data': formatted_report
                        })
                    else:
                        exported_reports.append({
                            'report_id': report_id,
                            'status': 'not_found',
                            'error': 'Report not found'
                        })

                except Exception as e:
                    logger.error(f"Failed to export report {report_id}: {e}")
                    exported_reports.append({
                        'report_id': report_id,
                        'status': 'error',
                        'error': str(e)
                    })

            # Create final export package
            export_package = self._create_export_package(
                exported_reports,
                export_format,
                compression
            )

            # Send webhook notification if provided
            if notification_webhook:
                self._send_webhook_notification(notification_webhook, {
                    'type': 'bulk_export_complete',
                    'total_reports': total_reports,
                    'successful': len([r for r in exported_reports if r['status'] == 'exported']),
                    'failed': len([r for r in exported_reports if r['status'] in ['error', 'not_found']])
                })

            return {
                'status': 'completed',
                'total_reports': total_reports,
                'successful': len([r for r in exported_reports if r['status'] == 'exported']),
                'failed': len([r for r in exported_reports if r['status'] in ['error', 'not_found']]),
                'export_package': export_package,
                'exported_reports': exported_reports
            }

        except Exception as e:
            logger.error(f"Bulk export failed: {e}")
            raise

    @job_service.create_task
    def scheduled_scan_task(self, scan_id, repository_url=None, file_patterns=None, template_id=None, llm_type="ollama", notification_webhook=None):
        """Execute a scheduled security scan"""
        try:
            # This would integrate with repository scanning logic
            # For now, simulate a scan

            self.update_state(state='PROGRESS', meta={
                'progress': 25,
                'stage': 'cloning_repository'
            })

            # Simulate repository cloning and file discovery
            await asyncio.sleep(2)

            self.update_state(state='PROGRESS', meta={
                'progress': 50,
                'stage': 'discovering_files'
            })

            # Simulate file pattern matching
            discovered_files = [
                {'path': 'src/main.py', 'content': '# Sample Python code'},
                {'path': 'src/utils.py', 'content': '# Utility functions'}
            ]

            self.update_state(state='PROGRESS', meta={
                'progress': 75,
                'stage': 'analyzing_files'
            })

            # Perform batch analysis
            batch_result = self.batch_analysis_task(
                files=discovered_files,
                analysis_config={'depth_level': 'standard'},
                llm_type=llm_type,
                template_id=template_id
            )

            # Send webhook notification if provided
            if notification_webhook:
                self._send_webhook_notification(notification_webhook, {
                    'type': 'scheduled_scan_complete',
                    'scan_id': scan_id,
                    'files_analyzed': len(discovered_files),
                    'vulnerabilities_found': 0  # Placeholder
                })

            return {
                'status': 'completed',
                'scan_id': scan_id,
                'repository_url': repository_url,
                'files_analyzed': len(discovered_files),
                'analysis_results': batch_result
            }

        except Exception as e:
            logger.error(f"Scheduled scan failed: {e}")
            raise

    def _load_report(self, report_id):
        """Load report from storage (placeholder)"""
        # This would load from actual report storage
        return {
            'id': report_id,
            'vulnerability_report': {'summary': 'Sample report'},
            'thought_process_log': 'Analysis steps...',
            'created_at': datetime.now().isoformat()
        }

    def _format_report_for_export(self, report_data, export_format, include_metadata, include_thought_process):
        """Format report for specific export format"""
        if export_format == 'json':
            return report_data
        elif export_format == 'csv':
            # Convert to CSV format
            return "id,summary,created_at\n" + f"{report_data['id']},Sample,{report_data['created_at']}"
        elif export_format == 'pdf':
            # Generate PDF (placeholder)
            return f"PDF content for report {report_data['id']}"
        else:
            return report_data

    def _create_export_package(self, exported_reports, export_format, compression):
        """Create final export package"""
        if compression:
            # Create ZIP archive
            return {
                'type': 'zip',
                'filename': f'bulk_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.zip',
                'size': '1.2MB'  # Placeholder
            }
        else:
            return {
                'type': export_format,
                'reports': exported_reports
            }

    def _generate_export(self, results, export_format):
        """Generate export in specified format"""
        if export_format == 'csv':
            csv_content = "file_path,status,vulnerabilities\n"
            for result in results:
                csv_content += f"{result['file_path']},{result['status']},0\n"
            return csv_content
        return None

    def _send_webhook_notification(self, webhook_url, data):
        """Send webhook notification (placeholder)"""
        logger.info(f"Sending webhook to {webhook_url}: {data}")
        # In production, this would make an HTTP POST request

    return {
        'analyze_code_batch': analyze_code_batch,
        'update_knowledge_base': update_knowledge_base,
        'generate_analytics_report': generate_analytics_report,
        'batch_analysis_task': batch_analysis_task,
        'bulk_export_task': bulk_export_task,
        'scheduled_scan_task': scheduled_scan_task
    }
