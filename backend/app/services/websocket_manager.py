import logging
from typing import Dict, Set, Optional, Any
from datetime import datetime
from fastapi import WebSocket
from backend.app.models.websocket_message import WebSocketMessage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ConnectionInfo:
    """Information about a WebSocket connection"""
    def __init__(self, websocket: WebSocket, user_id: Optional[str] = None, session_id: Optional[str] = None):
        self.websocket = websocket
        self.user_id = user_id
        self.session_id = session_id
        self.connected_at = datetime.now()
        self.channels: Set[str] = set()
        self.last_ping = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        return {
            "user_id": self.user_id,
            "session_id": self.session_id,
            "connected_at": self.connected_at.isoformat(),
            "channels": list(self.channels),
            "client_info": {
                "host": self.websocket.client.host if self.websocket.client else "unknown",
                "port": self.websocket.client.port if self.websocket.client else 0
            }
        }


class EnhancedWebSocketManager:
    """Enhanced WebSocket manager with channel support, authentication, and real-time features"""

    def __init__(self):
        # Connection management
        self.connections: Dict[WebSocket, ConnectionInfo] = {}
        self.channels: Dict[str, Set[WebSocket]] = {}
        self.user_connections: Dict[str, Set[WebSocket]] = {}

        # Statistics
        self.total_connections = 0
        self.total_messages_sent = 0
        self.start_time = datetime.now()

        logger.info("Enhanced WebSocket Manager initialized")

    async def connect(self, websocket: WebSocket, user_id: Optional[str] = None, session_id: Optional[str] = None):
        """Accept a new WebSocket connection"""
        try:
            await websocket.accept()

            # Create connection info
            connection_info = ConnectionInfo(websocket, user_id, session_id)
            self.connections[websocket] = connection_info

            # Track user connections
            if user_id:
                if user_id not in self.user_connections:
                    self.user_connections[user_id] = set()
                self.user_connections[user_id].add(websocket)

            # Update statistics
            self.total_connections += 1

            # Send welcome message
            welcome_message = WebSocketMessage(
                type="connection_established",
                payload={
                    "session_id": session_id,
                    "user_id": user_id,
                    "server_time": datetime.now().isoformat(),
                    "available_channels": list(self.channels.keys())
                }
            )
            await self.send_to_connection(websocket, welcome_message)

            logger.info(f"WebSocket connected: user_id={user_id}, session_id={session_id}")

        except Exception as e:
            logger.error(f"Error accepting WebSocket connection: {e}")
            raise

    def disconnect(self, websocket: WebSocket):
        """Disconnect a WebSocket connection"""
        try:
            if websocket not in self.connections:
                return

            connection_info = self.connections[websocket]

            # Remove from channels
            for channel in connection_info.channels.copy():
                self.leave_channel(websocket, channel)

            # Remove from user connections
            if connection_info.user_id and connection_info.user_id in self.user_connections:
                self.user_connections[connection_info.user_id].discard(websocket)
                if not self.user_connections[connection_info.user_id]:
                    del self.user_connections[connection_info.user_id]

            # Remove connection
            del self.connections[websocket]

            logger.info(f"WebSocket disconnected: user_id={connection_info.user_id}, session_id={connection_info.session_id}")

        except Exception as e:
            logger.error(f"Error disconnecting WebSocket: {e}")

    async def join_channel(self, websocket: WebSocket, channel: str) -> bool:
        """Add a connection to a channel"""
        try:
            if websocket not in self.connections:
                return False

            connection_info = self.connections[websocket]

            # Add to channel
            if channel not in self.channels:
                self.channels[channel] = set()

            self.channels[channel].add(websocket)
            connection_info.channels.add(channel)

            # Send confirmation
            message = WebSocketMessage(
                type="channel_joined",
                payload={
                    "channel": channel,
                    "members_count": len(self.channels[channel])
                }
            )
            await self.send_to_connection(websocket, message)

            logger.info(f"Connection joined channel: {channel}")
            return True

        except Exception as e:
            logger.error(f"Error joining channel {channel}: {e}")
            return False

    def leave_channel(self, websocket: WebSocket, channel: str) -> bool:
        """Remove a connection from a channel"""
        try:
            if websocket not in self.connections:
                return False

            connection_info = self.connections[websocket]

            # Remove from channel
            if channel in self.channels:
                self.channels[channel].discard(websocket)
                if not self.channels[channel]:
                    del self.channels[channel]

            connection_info.channels.discard(channel)

            logger.info(f"Connection left channel: {channel}")
            return True

        except Exception as e:
            logger.error(f"Error leaving channel {channel}: {e}")
            return False

    async def send_to_connection(self, websocket: WebSocket, message: WebSocketMessage):
        """Send a message to a specific connection"""
        try:
            if websocket in self.connections:
                message_json = message.model_dump_json()
                await websocket.send_text(message_json)
                self.total_messages_sent += 1

        except Exception as e:
            logger.error(f"Error sending message to connection: {e}")
            # Connection might be dead, remove it
            self.disconnect(websocket)

    async def send_to_user(self, user_id: str, message: WebSocketMessage):
        """Send a message to all connections of a specific user"""
        if user_id in self.user_connections:
            connections = self.user_connections[user_id].copy()
            for websocket in connections:
                await self.send_to_connection(websocket, message)

    async def send_to_channel(self, channel: str, message: WebSocketMessage, exclude: Optional[WebSocket] = None):
        """Send a message to all connections in a channel"""
        if channel in self.channels:
            connections = self.channels[channel].copy()
            for websocket in connections:
                if websocket != exclude:
                    await self.send_to_connection(websocket, message)

    async def broadcast(self, message: WebSocketMessage, exclude: Optional[WebSocket] = None):
        """Broadcast a message to all connections"""
        connections = list(self.connections.keys())
        for websocket in connections:
            if websocket != exclude:
                await self.send_to_connection(websocket, message)

    async def send_analysis_progress(self, job_id: str, progress: int, status: str, details: Optional[Dict] = None):
        """Send analysis progress update to relevant channels"""
        message = WebSocketMessage(
            type="analysis_progress",
            payload={
                "job_id": job_id,
                "progress": progress,
                "status": status,
                "details": details or {},
                "timestamp": datetime.now().isoformat()
            }
        )

        # Send to analysis channel and job-specific channel
        await self.send_to_channel("analysis", message)
        await self.send_to_channel(f"job_{job_id}", message)

    async def send_notification(self, notification_type: str, title: str, message: str,
                              severity: str = "info", user_id: Optional[str] = None,
                              channel: Optional[str] = None):
        """Send a notification"""
        notification_message = WebSocketMessage(
            type="notification",
            payload={
                "notification_type": notification_type,
                "title": title,
                "message": message,
                "severity": severity,
                "timestamp": datetime.now().isoformat()
            }
        )

        if user_id:
            await self.send_to_user(user_id, notification_message)
        elif channel:
            await self.send_to_channel(channel, notification_message)
        else:
            await self.broadcast(notification_message)

    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        return {
            "active_connections": len(self.connections),
            "total_connections": self.total_connections,
            "total_messages_sent": self.total_messages_sent,
            "channels": {
                channel: len(connections)
                for channel, connections in self.channels.items()
            },
            "users_online": len(self.user_connections),
            "uptime_seconds": (datetime.now() - self.start_time).total_seconds()
        }

    def get_connection_info(self, websocket: WebSocket) -> Optional[Dict[str, Any]]:
        """Get information about a specific connection"""
        if websocket in self.connections:
            return self.connections[websocket].to_dict()
        return None

    async def ping_connections(self):
        """Ping all connections to check if they're alive"""
        ping_message = WebSocketMessage(
            type="ping",
            payload={"timestamp": datetime.now().isoformat()}
        )

        dead_connections = []
        for websocket in list(self.connections.keys()):
            try:
                await self.send_to_connection(websocket, ping_message)
                self.connections[websocket].last_ping = datetime.now()
            except:
                dead_connections.append(websocket)

        # Clean up dead connections
        for websocket in dead_connections:
            self.disconnect(websocket)

        return len(dead_connections)


# Global WebSocket manager instance
websocket_manager = EnhancedWebSocketManager()

def get_websocket_manager() -> EnhancedWebSocketManager:
    """Get the global WebSocket manager instance"""
    return websocket_manager