"""
Workflow Automation Service

This service provides:
- Custom workflow definition and execution
- Workflow templates for common security processes
- Integration hooks and webhooks
- Automated workflow triggers
- Workflow monitoring and logging
"""

import json
import logging
import asyncio
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import uuid

from backend.app.services.background_jobs import get_background_job_service
from backend.app.services.bulk_operations_service import get_bulk_operations_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WorkflowStepType(Enum):
    """Types of workflow steps"""
    ANALYSIS = "analysis"
    EXPORT = "export"
    NOTIFICATION = "notification"
    CONDITION = "condition"
    DELAY = "delay"
    WEBHOOK = "webhook"
    CUSTOM = "custom"


class WorkflowStatus(Enum):
    """Workflow execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


@dataclass
class WorkflowStep:
    """Individual step in a workflow"""
    id: str
    name: str
    step_type: WorkflowStepType
    config: Dict[str, Any]
    depends_on: List[str] = None  # Step IDs this step depends on
    timeout_seconds: int = 3600  # 1 hour default
    retry_count: int = 3
    on_failure: str = "stop"  # stop, continue, retry


@dataclass
class Workflow:
    """Workflow definition"""
    id: str
    name: str
    description: str
    steps: List[WorkflowStep]
    triggers: List[Dict[str, Any]] = None  # Event triggers
    schedule: Optional[str] = None  # Cron schedule
    is_active: bool = True
    created_by: str = "system"
    created_at: datetime = None
    updated_at: datetime = None


@dataclass
class WorkflowExecution:
    """Workflow execution instance"""
    id: str
    workflow_id: str
    status: WorkflowStatus
    started_at: datetime
    completed_at: Optional[datetime] = None
    current_step: Optional[str] = None
    step_results: Dict[str, Any] = None
    error_message: Optional[str] = None
    triggered_by: str = "manual"
    context: Dict[str, Any] = None


class WorkflowAutomationService:
    """Service for workflow automation and execution"""
    
    def __init__(self):
        """Initialize the workflow automation service"""
        self.job_service = get_background_job_service()
        self.bulk_service = get_bulk_operations_service()
        
        # Storage for workflows and executions
        self.workflows: Dict[str, Workflow] = {}
        self.executions: Dict[str, WorkflowExecution] = {}
        
        # Step handlers
        self.step_handlers: Dict[WorkflowStepType, Callable] = {
            WorkflowStepType.ANALYSIS: self._handle_analysis_step,
            WorkflowStepType.EXPORT: self._handle_export_step,
            WorkflowStepType.NOTIFICATION: self._handle_notification_step,
            WorkflowStepType.CONDITION: self._handle_condition_step,
            WorkflowStepType.DELAY: self._handle_delay_step,
            WorkflowStepType.WEBHOOK: self._handle_webhook_step,
            WorkflowStepType.CUSTOM: self._handle_custom_step
        }
        
        # Initialize default workflows
        self._initialize_default_workflows()
        
        logger.info("Workflow Automation Service initialized")

    def _initialize_default_workflows(self):
        """Initialize default workflow templates"""
        default_workflows = [
            Workflow(
                id="security_scan_pipeline",
                name="Security Scan Pipeline",
                description="Complete security analysis pipeline with reporting",
                steps=[
                    WorkflowStep(
                        id="step_1",
                        name="Batch Analysis",
                        step_type=WorkflowStepType.ANALYSIS,
                        config={
                            "analysis_type": "batch",
                            "template_id": "python_web_app",
                            "llm_type": "ollama"
                        }
                    ),
                    WorkflowStep(
                        id="step_2",
                        name="Generate Report",
                        step_type=WorkflowStepType.EXPORT,
                        config={
                            "export_format": "pdf",
                            "include_metadata": True,
                            "include_thought_process": True
                        },
                        depends_on=["step_1"]
                    ),
                    WorkflowStep(
                        id="step_3",
                        name="Send Notification",
                        step_type=WorkflowStepType.NOTIFICATION,
                        config={
                            "notification_type": "email",
                            "recipients": ["<EMAIL>"],
                            "template": "security_scan_complete"
                        },
                        depends_on=["step_2"]
                    )
                ],
                created_at=datetime.now()
            ),
            Workflow(
                id="vulnerability_response",
                name="Vulnerability Response Workflow",
                description="Automated response to critical vulnerabilities",
                steps=[
                    WorkflowStep(
                        id="step_1",
                        name="Check Severity",
                        step_type=WorkflowStepType.CONDITION,
                        config={
                            "condition": "vulnerability.severity == 'critical'",
                            "true_action": "continue",
                            "false_action": "stop"
                        }
                    ),
                    WorkflowStep(
                        id="step_2",
                        name="Immediate Alert",
                        step_type=WorkflowStepType.NOTIFICATION,
                        config={
                            "notification_type": "slack",
                            "channel": "#security-alerts",
                            "message": "Critical vulnerability detected!"
                        },
                        depends_on=["step_1"]
                    ),
                    WorkflowStep(
                        id="step_3",
                        name="Create Ticket",
                        step_type=WorkflowStepType.WEBHOOK,
                        config={
                            "url": "https://jira.company.com/api/tickets",
                            "method": "POST",
                            "payload": {
                                "priority": "critical",
                                "summary": "Security vulnerability requires immediate attention"
                            }
                        },
                        depends_on=["step_2"]
                    )
                ],
                triggers=[
                    {
                        "event": "vulnerability_detected",
                        "condition": "severity == 'critical'"
                    }
                ],
                created_at=datetime.now()
            ),
            Workflow(
                id="scheduled_security_audit",
                name="Scheduled Security Audit",
                description="Weekly comprehensive security audit",
                steps=[
                    WorkflowStep(
                        id="step_1",
                        name="Repository Scan",
                        step_type=WorkflowStepType.ANALYSIS,
                        config={
                            "analysis_type": "repository",
                            "repository_url": "https://github.com/company/main-app",
                            "template_id": "api_security"
                        }
                    ),
                    WorkflowStep(
                        id="step_2",
                        name="Generate Weekly Report",
                        step_type=WorkflowStepType.EXPORT,
                        config={
                            "export_format": "pdf",
                            "report_type": "weekly_audit",
                            "include_trends": True
                        },
                        depends_on=["step_1"]
                    ),
                    WorkflowStep(
                        id="step_3",
                        name="Email Report",
                        step_type=WorkflowStepType.NOTIFICATION,
                        config={
                            "notification_type": "email",
                            "recipients": ["<EMAIL>", "<EMAIL>"],
                            "subject": "Weekly Security Audit Report",
                            "attach_report": True
                        },
                        depends_on=["step_2"]
                    )
                ],
                schedule="0 9 * * 1",  # Every Monday at 9 AM
                created_at=datetime.now()
            )
        ]
        
        for workflow in default_workflows:
            self.workflows[workflow.id] = workflow

    async def create_workflow(self, workflow: Workflow) -> bool:
        """Create a new workflow"""
        try:
            workflow.created_at = datetime.now()
            workflow.updated_at = datetime.now()
            self.workflows[workflow.id] = workflow
            
            logger.info(f"Created workflow: {workflow.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create workflow: {e}")
            return False

    async def execute_workflow(self, workflow_id: str, context: Dict[str, Any] = None, triggered_by: str = "manual") -> str:
        """Execute a workflow"""
        try:
            workflow = self.workflows.get(workflow_id)
            if not workflow:
                raise ValueError(f"Workflow {workflow_id} not found")
            
            if not workflow.is_active:
                raise ValueError(f"Workflow {workflow_id} is not active")
            
            # Create execution instance
            execution_id = str(uuid.uuid4())
            execution = WorkflowExecution(
                id=execution_id,
                workflow_id=workflow_id,
                status=WorkflowStatus.PENDING,
                started_at=datetime.now(),
                triggered_by=triggered_by,
                context=context or {},
                step_results={}
            )
            
            self.executions[execution_id] = execution
            
            # Submit workflow execution as background job
            job_id = await self.job_service.submit_job(
                "workflow_execution_task",
                execution_id=execution_id
            )
            
            logger.info(f"Started workflow execution {execution_id} for workflow {workflow_id}")
            return execution_id
            
        except Exception as e:
            logger.error(f"Failed to execute workflow: {e}")
            raise

    async def _execute_workflow_steps(self, execution_id: str):
        """Execute workflow steps in order"""
        execution = self.executions.get(execution_id)
        if not execution:
            raise ValueError(f"Execution {execution_id} not found")
        
        workflow = self.workflows.get(execution.workflow_id)
        if not workflow:
            raise ValueError(f"Workflow {execution.workflow_id} not found")
        
        try:
            execution.status = WorkflowStatus.RUNNING
            
            # Build dependency graph
            step_dependencies = {}
            for step in workflow.steps:
                step_dependencies[step.id] = step.depends_on or []
            
            # Execute steps in dependency order
            completed_steps = set()
            
            while len(completed_steps) < len(workflow.steps):
                # Find steps that can be executed (all dependencies completed)
                ready_steps = []
                for step in workflow.steps:
                    if (step.id not in completed_steps and 
                        all(dep in completed_steps for dep in step_dependencies[step.id])):
                        ready_steps.append(step)
                
                if not ready_steps:
                    raise RuntimeError("Circular dependency or unresolvable dependencies in workflow")
                
                # Execute ready steps
                for step in ready_steps:
                    try:
                        execution.current_step = step.id
                        
                        # Execute step
                        step_result = await self._execute_step(step, execution)
                        execution.step_results[step.id] = step_result
                        completed_steps.add(step.id)
                        
                        logger.info(f"Completed step {step.id} in execution {execution_id}")
                        
                    except Exception as e:
                        logger.error(f"Step {step.id} failed in execution {execution_id}: {e}")
                        
                        if step.on_failure == "stop":
                            execution.status = WorkflowStatus.FAILED
                            execution.error_message = str(e)
                            execution.completed_at = datetime.now()
                            return
                        elif step.on_failure == "continue":
                            execution.step_results[step.id] = {"status": "failed", "error": str(e)}
                            completed_steps.add(step.id)
                        # For retry, we could implement retry logic here
            
            execution.status = WorkflowStatus.COMPLETED
            execution.completed_at = datetime.now()
            
            logger.info(f"Workflow execution {execution_id} completed successfully")
            
        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.error_message = str(e)
            execution.completed_at = datetime.now()
            logger.error(f"Workflow execution {execution_id} failed: {e}")

    async def _execute_step(self, step: WorkflowStep, execution: WorkflowExecution) -> Dict[str, Any]:
        """Execute a single workflow step"""
        handler = self.step_handlers.get(step.step_type)
        if not handler:
            raise ValueError(f"No handler for step type {step.step_type}")
        
        return await handler(step, execution)

    # Step Handlers
    
    async def _handle_analysis_step(self, step: WorkflowStep, execution: WorkflowExecution) -> Dict[str, Any]:
        """Handle analysis workflow step"""
        config = step.config
        
        if config.get("analysis_type") == "batch":
            # Submit batch analysis
            files = execution.context.get("files", [])
            if not files:
                raise ValueError("No files provided for batch analysis")
            
            from backend.app.services.bulk_operations_service import BatchAnalysisRequest
            
            request = BatchAnalysisRequest(
                files=files,
                analysis_config=config.get("analysis_config", {}),
                llm_type=config.get("llm_type", "ollama"),
                template_id=config.get("template_id")
            )
            
            job_id = await self.bulk_service.submit_batch_analysis(request)
            
            # Wait for completion (simplified - in production, use proper job monitoring)
            await asyncio.sleep(5)  # Placeholder
            
            return {"status": "completed", "job_id": job_id}
        
        return {"status": "completed", "message": "Analysis step executed"}

    async def _handle_export_step(self, step: WorkflowStep, execution: WorkflowExecution) -> Dict[str, Any]:
        """Handle export workflow step"""
        config = step.config
        
        # Get report IDs from previous steps or context
        report_ids = execution.context.get("report_ids", [])
        
        if report_ids:
            from backend.app.services.bulk_operations_service import BulkExportRequest
            
            request = BulkExportRequest(
                report_ids=report_ids,
                export_format=config.get("export_format", "json"),
                include_metadata=config.get("include_metadata", True),
                include_thought_process=config.get("include_thought_process", True),
                compression=config.get("compression", False)
            )
            
            job_id = await self.bulk_service.submit_bulk_export(request)
            
            return {"status": "completed", "job_id": job_id}
        
        return {"status": "completed", "message": "Export step executed"}

    async def _handle_notification_step(self, step: WorkflowStep, execution: WorkflowExecution) -> Dict[str, Any]:
        """Handle notification workflow step"""
        config = step.config
        notification_type = config.get("notification_type", "email")
        
        # Simulate notification sending
        logger.info(f"Sending {notification_type} notification: {config}")
        
        return {"status": "completed", "notification_type": notification_type}

    async def _handle_condition_step(self, step: WorkflowStep, execution: WorkflowExecution) -> Dict[str, Any]:
        """Handle condition workflow step"""
        config = step.config
        condition = config.get("condition", "true")
        
        # Simple condition evaluation (in production, use safe expression evaluator)
        result = True  # Placeholder
        
        action = config.get("true_action" if result else "false_action", "continue")
        
        return {"status": "completed", "condition_result": result, "action": action}

    async def _handle_delay_step(self, step: WorkflowStep, execution: WorkflowExecution) -> Dict[str, Any]:
        """Handle delay workflow step"""
        config = step.config
        delay_seconds = config.get("delay_seconds", 60)
        
        await asyncio.sleep(delay_seconds)
        
        return {"status": "completed", "delayed_seconds": delay_seconds}

    async def _handle_webhook_step(self, step: WorkflowStep, execution: WorkflowExecution) -> Dict[str, Any]:
        """Handle webhook workflow step"""
        config = step.config
        url = config.get("url")
        method = config.get("method", "POST")
        payload = config.get("payload", {})
        
        # Simulate webhook call
        logger.info(f"Calling webhook {method} {url} with payload: {payload}")
        
        return {"status": "completed", "webhook_url": url, "method": method}

    async def _handle_custom_step(self, step: WorkflowStep, execution: WorkflowExecution) -> Dict[str, Any]:
        """Handle custom workflow step"""
        config = step.config
        
        # Custom step implementation would go here
        logger.info(f"Executing custom step: {config}")
        
        return {"status": "completed", "message": "Custom step executed"}

    # Management Methods
    
    def get_workflow(self, workflow_id: str) -> Optional[Workflow]:
        """Get a workflow by ID"""
        return self.workflows.get(workflow_id)

    def list_workflows(self) -> List[Workflow]:
        """List all workflows"""
        return list(self.workflows.values())

    def get_execution(self, execution_id: str) -> Optional[WorkflowExecution]:
        """Get a workflow execution by ID"""
        return self.executions.get(execution_id)

    def list_executions(self, workflow_id: Optional[str] = None) -> List[WorkflowExecution]:
        """List workflow executions"""
        executions = list(self.executions.values())
        
        if workflow_id:
            executions = [e for e in executions if e.workflow_id == workflow_id]
        
        return executions


# Global workflow automation service instance
workflow_automation_service = None

def get_workflow_automation_service() -> WorkflowAutomationService:
    """Get or create the global workflow automation service instance"""
    global workflow_automation_service
    
    if workflow_automation_service is None:
        workflow_automation_service = WorkflowAutomationService()
    
    return workflow_automation_service
