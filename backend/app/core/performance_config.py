"""
Performance and Scalability Configuration

This module provides configuration settings for:
- Cache configuration (Redis/Memory)
- Database connection settings
- Background job processing
- Performance monitoring
- Load balancing and scaling
"""

import os
from typing import Optional, Dict, Any
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class CacheConfig:
    """Cache configuration settings"""
    redis_url: Optional[str] = None
    default_ttl: int = 3600  # 1 hour
    max_memory_cache_size: int = 1000
    enable_compression: bool = True
    
    @classmethod
    def from_env(cls) -> 'CacheConfig':
        """Create cache config from environment variables"""
        return cls(
            redis_url=os.getenv('REDIS_URL', 'redis://localhost:6379/0'),
            default_ttl=int(os.getenv('CACHE_DEFAULT_TTL', '3600')),
            max_memory_cache_size=int(os.getenv('CACHE_MAX_MEMORY_SIZE', '1000')),
            enable_compression=os.getenv('CACHE_ENABLE_COMPRESSION', 'true').lower() == 'true'
        )


@dataclass
class DatabaseConfig:
    """Database configuration settings"""
    database_url: Optional[str] = None
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    enable_query_logging: bool = False
    
    @classmethod
    def from_env(cls) -> 'DatabaseConfig':
        """Create database config from environment variables"""
        return cls(
            database_url=os.getenv('DATABASE_URL', 'postgresql://user:password@localhost:5432/sentinel_trace'),
            pool_size=int(os.getenv('DB_POOL_SIZE', '10')),
            max_overflow=int(os.getenv('DB_MAX_OVERFLOW', '20')),
            pool_timeout=int(os.getenv('DB_POOL_TIMEOUT', '30')),
            enable_query_logging=os.getenv('DB_ENABLE_QUERY_LOGGING', 'false').lower() == 'true'
        )


@dataclass
class JobConfig:
    """Background job configuration settings"""
    broker_url: str = "redis://localhost:6379/0"
    result_backend: str = "redis://localhost:6379/0"
    task_time_limit: int = 1800  # 30 minutes
    task_soft_time_limit: int = 1500  # 25 minutes
    worker_prefetch_multiplier: int = 1
    task_acks_late: bool = True
    result_expires: int = 3600  # 1 hour
    
    @classmethod
    def from_env(cls) -> 'JobConfig':
        """Create job config from environment variables"""
        return cls(
            broker_url=os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0'),
            result_backend=os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0'),
            task_time_limit=int(os.getenv('CELERY_TASK_TIME_LIMIT', '1800')),
            task_soft_time_limit=int(os.getenv('CELERY_TASK_SOFT_TIME_LIMIT', '1500')),
            worker_prefetch_multiplier=int(os.getenv('CELERY_WORKER_PREFETCH_MULTIPLIER', '1')),
            task_acks_late=os.getenv('CELERY_TASK_ACKS_LATE', 'true').lower() == 'true',
            result_expires=int(os.getenv('CELERY_RESULT_EXPIRES', '3600'))
        )


@dataclass
class MonitoringConfig:
    """Performance monitoring configuration settings"""
    collection_interval: int = 30  # seconds
    retention_hours: int = 24
    enable_alerts: bool = True
    cpu_alert_threshold: float = 80.0
    memory_alert_threshold: float = 85.0
    disk_alert_threshold: float = 90.0
    response_time_alert_threshold: float = 5.0
    error_rate_alert_threshold: float = 5.0
    
    @classmethod
    def from_env(cls) -> 'MonitoringConfig':
        """Create monitoring config from environment variables"""
        return cls(
            collection_interval=int(os.getenv('MONITORING_COLLECTION_INTERVAL', '30')),
            retention_hours=int(os.getenv('MONITORING_RETENTION_HOURS', '24')),
            enable_alerts=os.getenv('MONITORING_ENABLE_ALERTS', 'true').lower() == 'true',
            cpu_alert_threshold=float(os.getenv('MONITORING_CPU_ALERT_THRESHOLD', '80.0')),
            memory_alert_threshold=float(os.getenv('MONITORING_MEMORY_ALERT_THRESHOLD', '85.0')),
            disk_alert_threshold=float(os.getenv('MONITORING_DISK_ALERT_THRESHOLD', '90.0')),
            response_time_alert_threshold=float(os.getenv('MONITORING_RESPONSE_TIME_ALERT_THRESHOLD', '5.0')),
            error_rate_alert_threshold=float(os.getenv('MONITORING_ERROR_RATE_ALERT_THRESHOLD', '5.0'))
        )


@dataclass
class ScalingConfig:
    """Scaling and load balancing configuration"""
    enable_load_balancing: bool = False
    max_workers: int = 4
    worker_timeout: int = 30
    max_requests_per_worker: int = 1000
    enable_auto_scaling: bool = False
    scale_up_threshold: float = 70.0  # CPU percentage
    scale_down_threshold: float = 30.0  # CPU percentage
    min_instances: int = 1
    max_instances: int = 10
    
    @classmethod
    def from_env(cls) -> 'ScalingConfig':
        """Create scaling config from environment variables"""
        return cls(
            enable_load_balancing=os.getenv('SCALING_ENABLE_LOAD_BALANCING', 'false').lower() == 'true',
            max_workers=int(os.getenv('SCALING_MAX_WORKERS', '4')),
            worker_timeout=int(os.getenv('SCALING_WORKER_TIMEOUT', '30')),
            max_requests_per_worker=int(os.getenv('SCALING_MAX_REQUESTS_PER_WORKER', '1000')),
            enable_auto_scaling=os.getenv('SCALING_ENABLE_AUTO_SCALING', 'false').lower() == 'true',
            scale_up_threshold=float(os.getenv('SCALING_SCALE_UP_THRESHOLD', '70.0')),
            scale_down_threshold=float(os.getenv('SCALING_SCALE_DOWN_THRESHOLD', '30.0')),
            min_instances=int(os.getenv('SCALING_MIN_INSTANCES', '1')),
            max_instances=int(os.getenv('SCALING_MAX_INSTANCES', '10'))
        )


@dataclass
class PerformanceConfig:
    """Complete performance and scalability configuration"""
    cache: CacheConfig
    database: DatabaseConfig
    jobs: JobConfig
    monitoring: MonitoringConfig
    scaling: ScalingConfig
    
    @classmethod
    def from_env(cls) -> 'PerformanceConfig':
        """Create complete performance config from environment variables"""
        return cls(
            cache=CacheConfig.from_env(),
            database=DatabaseConfig.from_env(),
            jobs=JobConfig.from_env(),
            monitoring=MonitoringConfig.from_env(),
            scaling=ScalingConfig.from_env()
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'cache': {
                'redis_url': self.cache.redis_url,
                'default_ttl': self.cache.default_ttl,
                'max_memory_cache_size': self.cache.max_memory_cache_size,
                'enable_compression': self.cache.enable_compression
            },
            'database': {
                'database_url': self.database.database_url,
                'pool_size': self.database.pool_size,
                'max_overflow': self.database.max_overflow,
                'pool_timeout': self.database.pool_timeout,
                'enable_query_logging': self.database.enable_query_logging
            },
            'jobs': {
                'broker_url': self.jobs.broker_url,
                'result_backend': self.jobs.result_backend,
                'task_time_limit': self.jobs.task_time_limit,
                'task_soft_time_limit': self.jobs.task_soft_time_limit,
                'worker_prefetch_multiplier': self.jobs.worker_prefetch_multiplier,
                'task_acks_late': self.jobs.task_acks_late,
                'result_expires': self.jobs.result_expires
            },
            'monitoring': {
                'collection_interval': self.monitoring.collection_interval,
                'retention_hours': self.monitoring.retention_hours,
                'enable_alerts': self.monitoring.enable_alerts,
                'cpu_alert_threshold': self.monitoring.cpu_alert_threshold,
                'memory_alert_threshold': self.monitoring.memory_alert_threshold,
                'disk_alert_threshold': self.monitoring.disk_alert_threshold,
                'response_time_alert_threshold': self.monitoring.response_time_alert_threshold,
                'error_rate_alert_threshold': self.monitoring.error_rate_alert_threshold
            },
            'scaling': {
                'enable_load_balancing': self.scaling.enable_load_balancing,
                'max_workers': self.scaling.max_workers,
                'worker_timeout': self.scaling.worker_timeout,
                'max_requests_per_worker': self.scaling.max_requests_per_worker,
                'enable_auto_scaling': self.scaling.enable_auto_scaling,
                'scale_up_threshold': self.scaling.scale_up_threshold,
                'scale_down_threshold': self.scaling.scale_down_threshold,
                'min_instances': self.scaling.min_instances,
                'max_instances': self.scaling.max_instances
            }
        }


# Global configuration instance
performance_config = PerformanceConfig.from_env()


def get_performance_config() -> PerformanceConfig:
    """Get the global performance configuration"""
    return performance_config


def update_performance_config(**kwargs) -> PerformanceConfig:
    """Update performance configuration with new values"""
    global performance_config
    
    # Update cache config
    if 'cache' in kwargs:
        cache_updates = kwargs['cache']
        for key, value in cache_updates.items():
            if hasattr(performance_config.cache, key):
                setattr(performance_config.cache, key, value)
    
    # Update database config
    if 'database' in kwargs:
        db_updates = kwargs['database']
        for key, value in db_updates.items():
            if hasattr(performance_config.database, key):
                setattr(performance_config.database, key, value)
    
    # Update job config
    if 'jobs' in kwargs:
        job_updates = kwargs['jobs']
        for key, value in job_updates.items():
            if hasattr(performance_config.jobs, key):
                setattr(performance_config.jobs, key, value)
    
    # Update monitoring config
    if 'monitoring' in kwargs:
        monitoring_updates = kwargs['monitoring']
        for key, value in monitoring_updates.items():
            if hasattr(performance_config.monitoring, key):
                setattr(performance_config.monitoring, key, value)
    
    # Update scaling config
    if 'scaling' in kwargs:
        scaling_updates = kwargs['scaling']
        for key, value in scaling_updates.items():
            if hasattr(performance_config.scaling, key):
                setattr(performance_config.scaling, key, value)
    
    return performance_config


# Environment variable validation
def validate_environment():
    """Validate that required environment variables are set"""
    required_vars = [
        'DATABASE_URL',
        'REDIS_URL'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"Warning: Missing environment variables: {', '.join(missing_vars)}")
        print("Using default values. For production, please set these variables.")
    
    return len(missing_vars) == 0


# Example .env file content
ENV_TEMPLATE = """
# Performance and Scalability Configuration

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
CACHE_DEFAULT_TTL=3600
CACHE_MAX_MEMORY_SIZE=1000
CACHE_ENABLE_COMPRESSION=true

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/sentinel_trace
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_ENABLE_QUERY_LOGGING=false

# Celery Job Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CELERY_TASK_TIME_LIMIT=1800
CELERY_TASK_SOFT_TIME_LIMIT=1500
CELERY_WORKER_PREFETCH_MULTIPLIER=1
CELERY_TASK_ACKS_LATE=true
CELERY_RESULT_EXPIRES=3600

# Performance Monitoring Configuration
MONITORING_COLLECTION_INTERVAL=30
MONITORING_RETENTION_HOURS=24
MONITORING_ENABLE_ALERTS=true
MONITORING_CPU_ALERT_THRESHOLD=80.0
MONITORING_MEMORY_ALERT_THRESHOLD=85.0
MONITORING_DISK_ALERT_THRESHOLD=90.0
MONITORING_RESPONSE_TIME_ALERT_THRESHOLD=5.0
MONITORING_ERROR_RATE_ALERT_THRESHOLD=5.0

# Scaling Configuration
SCALING_ENABLE_LOAD_BALANCING=false
SCALING_MAX_WORKERS=4
SCALING_WORKER_TIMEOUT=30
SCALING_MAX_REQUESTS_PER_WORKER=1000
SCALING_ENABLE_AUTO_SCALING=false
SCALING_SCALE_UP_THRESHOLD=70.0
SCALING_SCALE_DOWN_THRESHOLD=30.0
SCALING_MIN_INSTANCES=1
SCALING_MAX_INSTANCES=10
"""


def create_env_template(file_path: str = ".env.example"):
    """Create an example .env file with all configuration options"""
    with open(file_path, 'w') as f:
        f.write(ENV_TEMPLATE)
    print(f"Created environment template: {file_path}")


if __name__ == "__main__":
    # Validate environment and create template if needed
    if not validate_environment():
        create_env_template()
    
    # Print current configuration
    config = get_performance_config()
    print("Current Performance Configuration:")
    print("=" * 50)
    
    import json
    print(json.dumps(config.to_dict(), indent=2, default=str))
