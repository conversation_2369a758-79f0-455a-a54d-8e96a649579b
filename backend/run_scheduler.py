from rq_scheduler import Scheduler
from redis import Redis
from datetime import datetime, timedelta

# Connect to Redis
redis_conn = Redis(host='redis', port=6379, db=0)

# Initialize the scheduler
scheduler = Scheduler(connection=redis_conn)

def schedule_daily_vulnerability_report():
    """
    Schedules a daily vulnerability trends report.
    """
    from backend.app.tasks.analytics_tasks import generate_and_export_report_task
    # Schedule the task to run daily at a specific time (e.g., 2 AM)
    # For simplicity, user_id is hardcoded. In a real app, this would be dynamic.
    scheduler.schedule(
        scheduled_time=datetime.utcnow().replace(hour=2, minute=0, second=0, microsecond=0) + timedelta(days=1),
        func=generate_and_export_report_task,
        args=('vulnerability_trends', 'admin_user'),
        interval=timedelta(days=1).total_seconds(), # Run every 24 hours
        repeat=None # Repeat indefinitely
    )
    print("Daily vulnerability trends report scheduled.")

def schedule_weekly_model_performance_report():
    """
    Schedules a weekly model performance report.
    """
    from backend.app.tasks.analytics_tasks import generate_and_export_report_task
    # Schedule the task to run weekly on a specific day and time (e.g., Monday 3 AM)
    # For simplicity, user_id is hardcoded. In a real app, this would be dynamic.
    # Find the next Monday at 3 AM UTC
    now = datetime.utcnow()
    days_until_monday = (0 - now.weekday() + 7) % 7 # Monday is 0
    next_monday = (now + timedelta(days=days_until_monday)).replace(hour=3, minute=0, second=0, microsecond=0)
    if next_monday < now: # If Monday has already passed this week, schedule for next Monday
        next_monday += timedelta(weeks=1)

    scheduler.schedule(
        scheduled_time=next_monday,
        func=generate_and_export_report_task,
        args=('model_performance', 'admin_user'),
        interval=timedelta(weeks=1).total_seconds(), # Run every week
        repeat=None # Repeat indefinitely
    )
    print("Weekly model performance report scheduled.")

if __name__ == '__main__':
    print("Starting RQ Scheduler...")
    # Schedule initial jobs
    schedule_daily_vulnerability_report()
    schedule_weekly_model_performance_report()

    # The scheduler typically runs in a loop. For this simple script,
    # we'll just let it schedule and exit. In a production environment,
    # you'd run `rqscheduler` command.
    print("Scheduler setup complete. You can now run `rqworker` to process jobs.")
    print("To run the scheduler continuously, execute `rqscheduler` in your terminal.")